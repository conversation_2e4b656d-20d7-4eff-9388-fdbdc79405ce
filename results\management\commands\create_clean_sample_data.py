from django.core.management.base import BaseCommand
from results.models import Competition, Subject, Wilaya, School, Student, Result
from django.utils.text import slugify
import random
from faker import Faker

fake = Faker('ar_SA')

class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية منظمة ونظيفة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='حذف جميع البيانات الموجودة قبل الإنشاء',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('حذف البيانات الموجودة...')
            Result.objects.all().delete()
            Student.objects.all().delete()
            Competition.objects.all().delete()
            School.objects.all().delete()
            Subject.objects.all().delete()
            Wilaya.objects.all().delete()
            self.stdout.write('✅ تم حذف البيانات الموجودة')
        
        self.stdout.write('إنشاء بيانات تجريبية منظمة...')
        
        # 1. إنشاء الولايات
        wilayas = self.create_wilayas()
        
        # 2. إنشاء الشعب
        subjects = self.create_subjects()
        
        # 3. إنشاء المدارس
        schools = self.create_schools(wilayas)
        
        # 4. إنشاء المسابقات
        competitions = self.create_competitions()
        
        # 5. إنشاء الطلاب والنتائج
        self.create_students_and_results(competitions, subjects, schools)
        
        self.stdout.write(
            self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!')
        )
    
    def create_wilayas(self):
        """إنشاء الولايات الموريتانية"""
        self.stdout.write('إنشاء الولايات...')
        
        wilayas_data = [
            ('انواكشوط الشمالية', 'NKC'),
            ('انواكشوط الجنوبية', 'NKS'),
            ('انواكشوط الغربية', 'NKW'),
            ('داخلت انواذيبو', 'DN'),
            ('إينشيري', 'IC'),
            ('آدرار', 'AD'),
            ('تيرس زمور', 'TZ'),
            ('تكانت', 'TK'),
            ('البراكنة', 'BR'),
            ('ترارزة', 'TR'),
            ('كوركول', 'GO'),
            ('كيدي ماغا', 'GM'),
            ('لعصابة', 'AS'),
            ('كنكوصة', 'GD'),
            ('الحوض الشرقي', 'HE'),
            ('الحوض الغربي', 'HG')
        ]
        
        wilayas = []
        for name, code in wilayas_data:
            wilaya, created = Wilaya.objects.get_or_create(
                code=code,
                defaults={'name': name}
            )
            wilayas.append(wilaya)
            if created:
                self.stdout.write(f'  ✅ {name}')
        
        return wilayas
    
    def create_subjects(self):
        """إنشاء الشعب الدراسية"""
        self.stdout.write('إنشاء الشعب...')
        
        subjects_data = [
            ('العلوم الطبيعية', 'SN'),
            ('الرياضيات', 'MT'),
            ('الآداب الأصلية', 'LO'),
            ('الآداب العصرية', 'LM'),
            ('العلوم الإنسانية', 'SH'),
            ('التعليم التقني', 'TE'),
            ('العلوم الاقتصادية', 'SE')
        ]
        
        subjects = []
        for name, code in subjects_data:
            subject, created = Subject.objects.get_or_create(
                code=code,
                defaults={'name': name}
            )
            subjects.append(subject)
            if created:
                self.stdout.write(f'  ✅ {name}')
        
        return subjects
    
    def create_schools(self, wilayas):
        """إنشاء المدارس"""
        self.stdout.write('إنشاء المدارس...')
        
        school_names = [
            'ثانوية النجاح',
            'ثانوية التفوق',
            'ثانوية الأمل',
            'ثانوية المستقبل',
            'ثانوية العلم والمعرفة',
            'ثانوية الرسالة',
            'ثانوية الفجر',
            'ثانوية النور',
            'ثانوية التقدم',
            'ثانوية الإبداع'
        ]
        
        schools = []
        for wilaya in wilayas[:8]:  # أول 8 ولايات فقط
            for i, school_name in enumerate(school_names[:3]):  # 3 مدارس لكل ولاية
                full_name = f"{school_name} - {wilaya.name}"
                school, created = School.objects.get_or_create(
                    name=full_name,
                    wilaya=wilaya
                )
                schools.append(school)
                if created:
                    self.stdout.write(f'  ✅ {full_name}')
        
        return schools
    
    def create_competitions(self):
        """إنشاء المسابقات"""
        self.stdout.write('إنشاء المسابقات...')
        
        competitions_data = [
            ('باكالوريا 2025', 2025, 'bac-2025-newResults'),
            ('الباكلوريا 2024', 2024, 'bac-2024-uKolupoGL'),
            ('مسابقة ختم الدروس الإعدادية 2024', 2024, 'bepc-2024-eeIOq3sks'),
            ('مسابقة الإمتياز - الثانوية 2024', 2024, 'e5c-2024-1OijYskae')
        ]
        
        competitions = []
        for name, year, slug in competitions_data:
            competition, created = Competition.objects.get_or_create(
                slug=slug,
                defaults={
                    'name': name,
                    'year': year,
                    'is_active': True
                }
            )
            competitions.append(competition)
            if created:
                self.stdout.write(f'  ✅ {name}')
        
        return competitions
    
    def create_students_and_results(self, competitions, subjects, schools):
        """إنشاء الطلاب والنتائج"""
        self.stdout.write('إنشاء الطلاب والنتائج...')
        
        # أرقام البداية لكل مسابقة
        start_numbers = {
            'bac-2025-newResults': 40000,
            'bac-2024-uKolupoGL': 30000,
            'bepc-2024-eeIOq3sks': 20000,
            'e5c-2024-1OijYskae': 10000
        }
        
        total_created = 0
        
        for competition in competitions:
            start_num = start_numbers.get(competition.slug, 50000)
            
            # إنشاء 30 طالب لكل مسابقة
            for i in range(1, 31):
                # إنشاء الطالب
                student = Student.objects.create(
                    first_name=fake.first_name(),
                    last_name=fake.last_name(),
                    birth_date=fake.date_of_birth(minimum_age=16, maximum_age=22)
                )
                
                # إنشاء النتيجة
                average = round(random.uniform(8.0, 19.5), 2)
                
                Result.objects.create(
                    competition=competition,
                    student=student,
                    subject=random.choice(subjects),
                    school=random.choice(schools),
                    student_number=f"{start_num + i}",
                    average=average,
                    rank=i,  # سيتم إعادة حسابه لاحقاً
                    status='PASS' if average >= 10 else 'FAIL'
                )
                
                total_created += 1
            
            # إعادة حساب الترتيب الصحيح
            self.recalculate_competition_ranks(competition)
            
            self.stdout.write(f'  ✅ {competition.name}: 30 طالب')
        
        self.stdout.write(f'✅ تم إنشاء {total_created} طالب ونتيجة')
    
    def recalculate_competition_ranks(self, competition):
        """إعادة حساب الترتيب للمسابقة"""
        results = Result.objects.filter(
            competition=competition
        ).order_by('-average', 'student__first_name')
        
        for rank, result in enumerate(results, 1):
            result.rank = rank
            result.save(update_fields=['rank'])
