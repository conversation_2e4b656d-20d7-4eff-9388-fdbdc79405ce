from django.contrib import admin
from django.urls import path
from django.shortcuts import redirect
from django.utils.html import format_html
from .models import Wilaya, School, Subject, Competition, Student, Result


@admin.register(Wilaya)
class WilayaAdmin(admin.ModelAdmin):
    list_display = ['name', 'code']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(School)
class SchoolAdmin(admin.ModelAdmin):
    list_display = ['name', 'wilaya']
    list_filter = ['wilaya']
    search_fields = ['name']
    ordering = ['name']


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'code']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(Competition)
class CompetitionAdmin(admin.ModelAdmin):
    list_display = ['name', 'year', 'is_active', 'created_at']
    list_filter = ['year', 'is_active']
    search_fields = ['name']
    prepopulated_fields = {'slug': ('name', 'year')}
    ordering = ['-year', 'name']


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['student_number', 'first_name', 'last_name', 'gender']
    search_fields = ['student_number', 'first_name', 'last_name']
    list_filter = ['gender']
    ordering = ['last_name', 'first_name']


@admin.register(Result)
class ResultAdmin(admin.ModelAdmin):
    list_display = ['student_number', 'student', 'competition', 'subject', 'school', 'average', 'rank', 'status']
    list_filter = ['competition', 'subject', 'school__wilaya', 'status']
    search_fields = ['student_number', 'student__first_name', 'student__last_name']
    ordering = ['-average']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'student', 'competition', 'subject', 'school', 'school__wilaya'
        )

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['import_excel_url'] = '/admin/import-excel/'
        return super().changelist_view(request, extra_context=extra_context)
