from django.core.management.base import BaseCommand
from results.models import Competition, Student, Result, School, Subject, Wilaya

class Command(BaseCommand):
    help = 'عرض إحصائيات قاعدة البيانات'

    def handle(self, *args, **options):
        self.stdout.write('📊 إحصائيات قاعدة البيانات:')
        self.stdout.write('=' * 40)
        
        competitions_count = Competition.objects.count()
        students_count = Student.objects.count()
        results_count = Result.objects.count()
        schools_count = School.objects.count()
        subjects_count = Subject.objects.count()
        wilayas_count = Wilaya.objects.count()
        
        self.stdout.write(f'🏆 المسابقات: {competitions_count}')
        self.stdout.write(f'👥 الطلاب: {students_count}')
        self.stdout.write(f'📋 النتائج: {results_count}')
        self.stdout.write(f'🏫 المدارس: {schools_count}')
        self.stdout.write(f'📚 الشعب: {subjects_count}')
        self.stdout.write(f'🌍 الولايات: {wilayas_count}')
        
        # إحصائيات النتائج
        if results_count > 0:
            self.stdout.write('\n📈 إحصائيات النتائج:')
            self.stdout.write('-' * 30)
            
            passed_count = Result.objects.filter(status='PASS').count()
            session_count = Result.objects.filter(status='SESSION').count()
            failed_count = Result.objects.filter(status='FAIL').count()
            absent_count = Result.objects.filter(status='ABSENT').count()
            
            self.stdout.write(f'✅ ناجح: {passed_count} ({round(passed_count/results_count*100, 1)}%)')
            self.stdout.write(f'⚠️ Session: {session_count} ({round(session_count/results_count*100, 1)}%)')
            self.stdout.write(f'❌ راسب: {failed_count} ({round(failed_count/results_count*100, 1)}%)')
            self.stdout.write(f'👻 غائب: {absent_count} ({round(absent_count/results_count*100, 1)}%)')
        
        # المسابقة النشطة
        active_competition = Competition.objects.filter(is_active=True).first()
        if active_competition:
            self.stdout.write(f'\n🏆 المسابقة النشطة: {active_competition.name} ({active_competition.year})')
            active_results = Result.objects.filter(competition=active_competition).count()
            self.stdout.write(f'   📋 النتائج: {active_results}')
        
        self.stdout.write('\n' + '=' * 40)
        self.stdout.write('✅ تم عرض الإحصائيات بنجاح!')
