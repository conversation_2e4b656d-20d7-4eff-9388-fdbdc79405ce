#!/usr/bin/env python
"""
سكريبت لتشغيل جميع الاختبارات
"""
import os
import sys
import subprocess

def run_command(command, description):
    """تشغيل أمر مع عرض الوصف"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - تم بنجاح")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في {description}")
        if e.stderr:
            print(f"خطأ: {e.stderr}")
        if e.stdout:
            print(f"إخراج: {e.stdout}")
        return False

def main():
    print("🧪 بدء تشغيل اختبارات موقع موريباك...")
    
    # تشغيل اختبارات Django
    print("\n📋 تشغيل اختبارات Django...")
    if not run_command("python manage.py test --settings=mauribac_results.settings_sqlite", "اختبارات Django"):
        print("❌ فشلت بعض الاختبارات")
        return False
    
    # فحص جودة الكود
    print("\n🔍 فحص جودة الكود...")
    
    # فحص بناء الجملة
    if not run_command("python -m py_compile manage.py", "فحص ملف manage.py"):
        return False
    
    if not run_command("python -m py_compile results/models.py", "فحص ملف النماذج"):
        return False
    
    if not run_command("python -m py_compile results/views.py", "فحص ملف العروض"):
        return False
    
    # اختبار تطبيق الهجرات
    print("\n🔄 اختبار الهجرات...")
    if not run_command("python manage.py makemigrations --dry-run --settings=mauribac_results.settings_sqlite", "فحص الهجرات"):
        return False
    
    # اختبار جمع الملفات الثابتة
    print("\n📁 اختبار جمع الملفات الثابتة...")
    if not run_command("python manage.py collectstatic --noinput --dry-run --settings=mauribac_results.settings_sqlite", "جمع الملفات الثابتة"):
        return False
    
    # اختبار الروابط
    print("\n🔗 اختبار الروابط...")
    if not run_command("python manage.py check --settings=mauribac_results.settings_sqlite", "فحص النظام"):
        return False
    
    # اختبار الأمان
    print("\n🔒 اختبار الأمان...")
    if not run_command("python manage.py check --deploy --settings=mauribac_results.settings_production", "فحص الأمان"):
        print("⚠️ تحذير: توجد مشاكل أمنية محتملة")
    
    print("\n✅ تم إنجاز جميع الاختبارات بنجاح!")
    print("🎉 الموقع جاهز للاستخدام!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
