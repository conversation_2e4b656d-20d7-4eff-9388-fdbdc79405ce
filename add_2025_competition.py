#!/usr/bin/env python
"""
إضافة مسابقة الباكلوريا 2025 مع نتائج تجريبية
"""
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mauribac_results.settings')
django.setup()

from results.models import Competition, Subject, Wilaya, School, Student, Result
from decimal import Decimal
import random

def create_2025_competition():
    """إنشاء مسابقة الباكلوريا 2025"""
    
    # إنشاء المسابقة
    competition, created = Competition.objects.get_or_create(
        slug='bac-2025-newResults',
        defaults={
            'name': 'الباكلوريا 2025',
            'year': 2025,
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ تم إنشاء مسابقة: {competition.name}")
    else:
        print(f"✅ المسابقة موجودة: {competition.name}")
    
    return competition

def add_2025_results():
    """إضافة نتائج تجريبية لسنة 2025"""
    
    competition = create_2025_competition()
    
    # بيانات الطلاب الجدد لسنة 2025
    students_data = [
        # العلوم الطبيعية
        ('40001', 'محمد', 'عبد الله', 'M', 'العلوم الطبيعية', 'ثانوية التميز', 'انواكشوط 1 (الشمالية)', 18.50),
        ('40002', 'فاطمة', 'أحمد', 'F', 'العلوم الطبيعية', 'ثانوية الإبداع', 'انواكشوط 2 (الغربية)', 17.80),
        ('40003', 'عبد الرحمن', 'محمد', 'M', 'العلوم الطبيعية', 'ثانوية المستقبل الجديد', 'انواكشوط 3 (الجنوبية)', 17.25),
        ('40004', 'عائشة', 'علي', 'F', 'العلوم الطبيعية', 'ثانوية النور', 'داخلت انواذيبو', 16.90),
        ('40005', 'أحمد', 'إبراهيم', 'M', 'العلوم الطبيعية', 'ثانوية الرقي', 'تيرس زمور', 16.45),
        
        # الرياضيات
        ('40006', 'مريم', 'محمد', 'F', 'الرياضيات', 'ثانوية الأرقام', 'آدرار', 18.75),
        ('40007', 'يوسف', 'عمر', 'M', 'الرياضيات', 'معهد الرياضيات المتقدم', 'كوركل', 18.20),
        ('40008', 'خديجة', 'حسن', 'F', 'الرياضيات', 'ثانوية الحساب', 'لعصابه', 17.95),
        ('40009', 'عمر', 'علي', 'M', 'الرياضيات', 'ثانوية الجبر', 'البراكنه', 17.60),
        ('40010', 'زينب', 'محمد', 'F', 'الرياضيات', 'ثانوية الهندسة', 'الترارزه', 17.30),
        
        # الآداب الأصلية
        ('40011', 'إبراهيم', 'أحمد', 'M', 'الآداب الأصلية', 'ثانوية التراث', 'انواكشوط 1 (الشمالية)', 16.80),
        ('40012', 'آمنة', 'محمد', 'F', 'الآداب الأصلية', 'ثانوية الأصالة', 'انواكشوط 2 (الغربية)', 16.50),
        ('40013', 'محمد', 'يوسف', 'M', 'الآداب الأصلية', 'ثانوية الفصاحة', 'انواكشوط 3 (الجنوبية)', 16.20),
        ('40014', 'حفصة', 'علي', 'F', 'الآداب الأصلية', 'ثانوية البلاغة', 'داخلت انواذيبو', 15.95),
        ('40015', 'عبد الله', 'حسن', 'M', 'الآداب الأصلية', 'ثانوية النحو', 'تيرس زمور', 15.70),
        
        # الآداب العصرية
        ('40016', 'سلمى', 'إبراهيم', 'F', 'الآداب العصرية', 'ثانوية الحداثة', 'آدرار', 16.40),
        ('40017', 'حسن', 'محمد', 'M', 'الآداب العصرية', 'ثانوية العصر', 'كوركل', 16.10),
        ('40018', 'نور', 'أحمد', 'F', 'الآداب العصرية', 'ثانوية التطوير', 'لعصابه', 15.85),
        ('40019', 'علي', 'عمر', 'M', 'الآداب العصرية', 'ثانوية التجديد', 'البراكنه', 15.60),
        ('40020', 'ليلى', 'يوسف', 'F', 'الآداب العصرية', 'ثانوية الإبداع الأدبي', 'الترارزه', 15.35),
        
        # التقنية
        ('40021', 'محمد', 'علي', 'M', 'التقنية', 'المعهد التقني المتطور', 'انواكشوط 1 (الشمالية)', 15.80),
        ('40022', 'فاطمة', 'حسن', 'F', 'التقنية', 'ثانوية التكنولوجيا', 'انواكشوط 2 (الغربية)', 15.50),
        ('40023', 'أحمد', 'محمد', 'M', 'التقنية', 'معهد الصناعة', 'انواكشوط 3 (الجنوبية)', 15.25),
        ('40024', 'عائشة', 'إبراهيم', 'F', 'التقنية', 'ثانوية الميكانيك', 'داخلت انواذيبو', 15.00),
        ('40025', 'يوسف', 'علي', 'M', 'التقنية', 'معهد الكهرباء', 'تيرس زمور', 14.75),
        
        # الهندسة الكهربائية
        ('40026', 'مريم', 'أحمد', 'F', 'الهندسة الكهربائية', 'معهد الهندسة المتقدم', 'آدرار', 17.40),
        ('40027', 'عبد الرحمن', 'محمد', 'M', 'الهندسة الكهربائية', 'ثانوية الإلكترونيات', 'كوركل', 17.10),
        ('40028', 'خديجة', 'يوسف', 'F', 'الهندسة الكهربائية', 'معهد الطاقة', 'لعصابه', 16.85),
        ('40029', 'إبراهيم', 'علي', 'M', 'الهندسة الكهربائية', 'ثانوية الدوائر', 'البراكنه', 16.60),
        ('40030', 'زينب', 'حسن', 'F', 'الهندسة الكهربائية', 'معهد التحكم', 'الترارزه', 16.35),
        
        # اللغات
        ('40031', 'محمد', 'عمر', 'M', 'اللغات', 'ثانوية اللسان', 'انواكشوط 1 (الشمالية)', 16.70),
        ('40032', 'فاطمة', 'علي', 'F', 'اللغات', 'معهد الترجمة', 'انواكشوط 2 (الغربية)', 16.45),
        ('40033', 'أحمد', 'إبراهيم', 'M', 'اللغات', 'ثانوية التواصل', 'انواكشوط 3 (الجنوبية)', 16.20),
        ('40034', 'عائشة', 'محمد', 'F', 'اللغات', 'معهد اللغات الحية', 'داخلت انواذيبو', 15.95),
        ('40035', 'يوسف', 'أحمد', 'M', 'اللغات', 'ثانوية الألسن', 'تيرس زمور', 15.70),
    ]
    
    print(f"\n📊 إضافة {len(students_data)} طالب لمسابقة {competition.name}...")
    
    created_count = 0
    updated_count = 0
    
    for student_data in students_data:
        student_number, first_name, last_name, gender, subject_name, school_name, wilaya_name, average = student_data
        
        try:
            # البحث عن أو إنشاء الكائنات المطلوبة
            subject, _ = Subject.objects.get_or_create(
                name=subject_name,
                defaults={'code': subject_name[:10].lower().replace(' ', '_')}
            )
            
            wilaya, _ = Wilaya.objects.get_or_create(
                name=wilaya_name,
                defaults={'code': wilaya_name[:10].upper().replace(' ', '_')}
            )
            
            school, _ = School.objects.get_or_create(
                name=school_name,
                defaults={'wilaya': wilaya}
            )
            
            # إنشاء أو تحديث الطالب
            student, _ = Student.objects.get_or_create(
                student_number=student_number,
                defaults={
                    'first_name': first_name,
                    'last_name': last_name,
                    'gender': gender
                }
            )
            
            # إنشاء أو تحديث النتيجة
            result, created = Result.objects.get_or_create(
                student_number=student_number,
                competition=competition,
                defaults={
                    'student': student,
                    'subject': subject,
                    'school': school,
                    'average': Decimal(str(average)),
                    'status': 'PASS' if average >= 10 else 'FAIL'
                }
            )
            
            if created:
                created_count += 1
            else:
                updated_count += 1
                
        except Exception as e:
            print(f"❌ خطأ في إضافة الطالب {student_number}: {e}")
    
    print(f"✅ تم إنشاء {created_count} نتيجة جديدة")
    if updated_count > 0:
        print(f"🔄 تم تحديث {updated_count} نتيجة موجودة")
    
    # تحديث الترتيب
    update_rankings_2025(competition)
    
    return competition

def update_rankings_2025(competition):
    """تحديث ترتيب نتائج 2025"""
    
    print("\n📈 تحديث الترتيب...")
    
    # الترتيب العام
    results = Result.objects.filter(competition=competition).order_by('-average')
    for rank, result in enumerate(results, 1):
        result.rank = rank
        result.save(update_fields=['rank'])
    
    # الترتيب في كل شعبة
    for subject in Subject.objects.all():
        subject_results = Result.objects.filter(
            competition=competition,
            subject=subject
        ).order_by('-average')
        
        for rank, result in enumerate(subject_results, 1):
            result.rank_in_subject = rank
            result.save(update_fields=['rank_in_subject'])
    
    print("✅ تم تحديث الترتيب بنجاح")

def main():
    print("🚀 إضافة نتائج الباكلوريا 2025...")
    
    try:
        competition = add_2025_results()
        
        print(f"\n🎉 تم إنجاز إضافة نتائج {competition.name} بنجاح!")
        print(f"🔗 رابط المسابقة: http://127.0.0.1:8000/{competition.slug}/")
        print(f"🔗 رابط البحث: http://127.0.0.1:8000/{competition.slug}/?search=40001")
        
        # إحصائيات سريعة
        total_results = Result.objects.filter(competition=competition).count()
        subjects_count = Result.objects.filter(competition=competition).values('subject').distinct().count()
        
        print(f"\n📊 إحصائيات المسابقة:")
        print(f"   - إجمالي النتائج: {total_results}")
        print(f"   - عدد الشعب: {subjects_count}")
        print(f"   - أعلى معدل: {Result.objects.filter(competition=competition).order_by('-average').first().average}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
