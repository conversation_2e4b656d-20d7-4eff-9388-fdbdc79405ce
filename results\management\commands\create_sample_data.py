from django.core.management.base import BaseCommand
from django.db import transaction
from results.models import Wilaya, School, Subject, Competition, Student, Result
import random
from decimal import Decimal


class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للموقع'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء إنشاء البيانات التجريبية...'))
        
        with transaction.atomic():
            # إنشاء الولايات
            self.create_wilayas()
            
            # إنشاء الشعب
            self.create_subjects()
            
            # إنشاء المدارس
            self.create_schools()
            
            # إنشاء المسابقات
            self.create_competitions()
            
            # إنشاء الطلاب والنتائج
            self.create_students_and_results()
        
        self.stdout.write(self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!'))

    def create_wilayas(self):
        wilayas_data = [
            ('انواكشوط 1 (الشمالية)', 'NKC1'),
            ('انواكشوط 2 (الغربية)', 'NKC2'),
            ('انواكشوط 3 (الجنوبية)', 'NKC3'),
            ('داخلت انواذيبو', 'DKLT'),
            ('تيرس زمور', 'TRS'),
            ('آدرار', 'ADR'),
            ('كوركل', 'KRKL'),
            ('لعصابه', 'ASBA'),
            ('البراكنه', 'BRKN'),
            ('الترارزه', 'TRRZ'),
        ]
        
        for name, code in wilayas_data:
            wilaya, created = Wilaya.objects.get_or_create(
                code=code,
                defaults={'name': name}
            )
            if created:
                self.stdout.write(f'تم إنشاء الولاية: {name}')

    def create_subjects(self):
        subjects_data = [
            ('العلوم الطبيعية', 'sn'),
            ('الرياضيات', 'm'),
            ('الآداب الأصلية', 'lo'),
            ('الآداب العصرية', 'lm'),
            ('التقنية', 'tm'),
            ('الهندسة الكهربائية', 'ts'),
            ('اللغات', 'la'),
        ]
        
        for name, code in subjects_data:
            subject, created = Subject.objects.get_or_create(
                code=code,
                defaults={'name': name}
            )
            if created:
                self.stdout.write(f'تم إنشاء الشعبة: {name}')

    def create_schools(self):
        schools_data = [
            ('المواهب الجديدة', 'NKC1'),
            ('ثانوية الامتياز 1', 'NKC2'),
            ('ثانوية الامتياز 3', 'NKC1'),
            ('الإصلاح الرائد - عرفات', 'NKC3'),
            ('مدينة العلوم', 'NKC1'),
            ('الصفوة الحديثة عرفات', 'NKC3'),
            ('العمرية عرفات', 'NKC3'),
            ('م ت ف ت م بانواكشوط', 'NKC2'),
            ('الثانوية الفنية - ازويرات', 'TRS'),
            ('ثانوية الامتياز - انواذيبو', 'DKLT'),
            ('مدرسة الرواد الحرة - كيفه', 'ASBA'),
            ('ثانوية لكصيبه 1', 'KRKL'),
            ('ثانوية انواذيبو 3', 'DKLT'),
            ('م ت ف ت م باطار', 'ADR'),
        ]
        
        for school_name, wilaya_code in schools_data:
            wilaya = Wilaya.objects.get(code=wilaya_code)
            school, created = School.objects.get_or_create(
                name=school_name,
                defaults={'wilaya': wilaya}
            )
            if created:
                self.stdout.write(f'تم إنشاء المدرسة: {school_name}')

    def create_competitions(self):
        competitions_data = [
            ('الباكلوريا 2024', 2024, 'bac-2024-uKolupoGL'),
            ('مسابقة ختم الدروس الإعدادية 2024', 2024, 'bepc-2024-eeIOq3sks'),
            ('مسابقة الإمتياز - الثانوية 2024', 2024, 'e5c-2024-1OijYskae'),
        ]
        
        for name, year, slug in competitions_data:
            competition, created = Competition.objects.get_or_create(
                slug=slug,
                defaults={'name': name, 'year': year}
            )
            if created:
                self.stdout.write(f'تم إنشاء المسابقة: {name}')

    def create_students_and_results(self):
        # أسماء تجريبية
        first_names = [
            'محمد', 'أحمد', 'عبد الله', 'إبراهيم', 'عمر', 'علي', 'حسن', 'يوسف',
            'فاطمة', 'عائشة', 'خديجة', 'مريم', 'زينب', 'أم كلثوم', 'آمنة', 'حفصة'
        ]
        
        last_names = [
            'محمد', 'أحمد', 'عبد الله', 'إبراهيم', 'عمر', 'علي', 'حسن', 'يوسف',
            'الصديق', 'البخاري', 'الحر', 'ناصر الدين', 'عبد الرحمن', 'المختار'
        ]
        
        competition = Competition.objects.get(slug='bac-2024-uKolupoGL')
        subjects = Subject.objects.all()
        schools = School.objects.all()
        
        student_number = 10000
        
        for subject in subjects:
            # عدد الطلاب لكل شعبة
            if subject.code == 'sn':
                num_students = 50
            elif subject.code == 'lo':
                num_students = 30
            elif subject.code == 'lm':
                num_students = 25
            elif subject.code == 'm':
                num_students = 20
            else:
                num_students = 15
            
            for i in range(num_students):
                student_number += 1
                
                # إنشاء الطالب
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                gender = random.choice(['M', 'F'])
                
                student, created = Student.objects.get_or_create(
                    student_number=str(student_number),
                    defaults={
                        'first_name': first_name,
                        'last_name': last_name,
                        'gender': gender
                    }
                )
                
                # إنشاء النتيجة
                school = random.choice(schools)
                
                # توليد معدل عشوائي
                if i < 3:  # أفضل 3 طلاب
                    average = round(random.uniform(16.0, 18.0), 2)
                elif i < 10:  # الطلاب الجيدون
                    average = round(random.uniform(14.0, 16.0), 2)
                else:  # باقي الطلاب
                    average = round(random.uniform(10.0, 14.0), 2)
                
                result, created = Result.objects.get_or_create(
                    student=student,
                    competition=competition,
                    student_number=str(student_number),
                    defaults={
                        'subject': subject,
                        'school': school,
                        'average': Decimal(str(average)),
                        'status': 'PASS' if average >= 10 else 'FAIL'
                    }
                )
                
                if created:
                    self.stdout.write(f'تم إنشاء نتيجة للطالب: {student.full_name}')
        
        # تحديث الترتيب
        self.update_rankings()

    def update_rankings(self):
        competition = Competition.objects.get(slug='bac-2024-uKolupoGL')
        
        # الترتيب العام
        results = Result.objects.filter(competition=competition).order_by('-average')
        for rank, result in enumerate(results, 1):
            result.rank = rank
            result.save()
        
        # الترتيب في كل شعبة
        for subject in Subject.objects.all():
            subject_results = Result.objects.filter(
                competition=competition,
                subject=subject
            ).order_by('-average')
            
            for rank, result in enumerate(subject_results, 1):
                result.rank_in_subject = rank
                result.save()
        
        self.stdout.write('تم تحديث الترتيب بنجاح')
