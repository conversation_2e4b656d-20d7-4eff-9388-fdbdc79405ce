from django.shortcuts import render, get_object_or_404
from django.db.models import Q, Count
from django.http import Http404
from .models import Competition, Result, Subject, Wilaya, School
from urllib.parse import unquote


def home(request):
    """الصفحة الرئيسية - عرض قائمة المسابقات"""
    competitions = Competition.objects.filter(is_active=True).order_by('-year', 'name')
    return render(request, 'results/home.html', {
        'competitions': competitions
    })


def competition_detail(request, slug):
    """صفحة تفاصيل المسابقة مع البحث"""
    competition = get_object_or_404(Competition, slug=slug, is_active=True)
    
    # إحصائيات المسابقة
    results = Result.objects.filter(competition=competition)
    subjects_stats = results.values('subject__name', 'subject__code').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # البحث برقم الطالب
    search_query = request.GET.get('search', '')
    search_result = None
    if search_query:
        try:
            search_result = Result.objects.select_related(
                'student', 'subject', 'school', 'school__wilaya'
            ).get(
                competition=competition,
                student_number=search_query
            )
        except Result.DoesNotExist:
            pass
    
    # أفضل 3 طلاب من كل شعبة
    top_students_by_subject = {}
    for subject_stat in subjects_stats:
        subject_code = subject_stat['subject__code']
        top_students = results.filter(
            subject__code=subject_code
        ).select_related(
            'student', 'subject', 'school', 'school__wilaya'
        ).order_by('-average')[:3]
        
        if top_students:
            top_students_by_subject[subject_stat['subject__name']] = {
                'code': subject_code,
                'students': top_students
            }
    
    return render(request, 'results/competition_detail.html', {
        'competition': competition,
        'subjects_stats': subjects_stats,
        'search_query': search_query,
        'search_result': search_result,
        'top_students_by_subject': top_students_by_subject,
    })


def student_result(request, slug, student_number):
    """صفحة نتيجة طالب محدد"""
    competition = get_object_or_404(Competition, slug=slug, is_active=True)
    result = get_object_or_404(
        Result.objects.select_related('student', 'subject', 'school', 'school__wilaya'),
        competition=competition,
        student_number=student_number
    )
    
    return render(request, 'results/student_result.html', {
        'competition': competition,
        'result': result,
    })


def subject_results(request, slug, subject_code):
    """صفحة نتائج شعبة محددة"""
    competition = get_object_or_404(Competition, slug=slug, is_active=True)
    subject = get_object_or_404(Subject, code=subject_code)
    
    results = Result.objects.filter(
        competition=competition,
        subject=subject
    ).select_related(
        'student', 'school', 'school__wilaya'
    ).order_by('-average')
    
    # إحصائيات
    total_students = results.count()
    passed_students = results.filter(status='PASS').count()
    
    return render(request, 'results/subject_results.html', {
        'competition': competition,
        'subject': subject,
        'results': results,
        'total_students': total_students,
        'passed_students': passed_students,
    })


def school_results(request, slug, subject_code, school_name):
    """صفحة نتائج مدرسة محددة في شعبة محددة"""
    competition = get_object_or_404(Competition, slug=slug, is_active=True)
    subject = get_object_or_404(Subject, code=subject_code)
    
    # فك تشفير اسم المدرسة
    school_name_decoded = unquote(school_name)
    school = get_object_or_404(School, name=school_name_decoded)
    
    results = Result.objects.filter(
        competition=competition,
        subject=subject,
        school=school
    ).select_related('student').order_by('-average')
    
    return render(request, 'results/school_results.html', {
        'competition': competition,
        'subject': subject,
        'school': school,
        'results': results,
    })


def wilaya_results(request, slug, subject_code, wilaya_name):
    """صفحة نتائج ولاية محددة في شعبة محددة"""
    competition = get_object_or_404(Competition, slug=slug, is_active=True)
    subject = get_object_or_404(Subject, code=subject_code)
    
    # فك تشفير اسم الولاية
    wilaya_name_decoded = unquote(wilaya_name)
    wilaya = get_object_or_404(Wilaya, name=wilaya_name_decoded)
    
    results = Result.objects.filter(
        competition=competition,
        subject=subject,
        school__wilaya=wilaya
    ).select_related('student', 'school').order_by('-average')
    
    return render(request, 'results/wilaya_results.html', {
        'competition': competition,
        'subject': subject,
        'wilaya': wilaya,
        'results': results,
    })
