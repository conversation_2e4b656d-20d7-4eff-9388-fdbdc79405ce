@echo off
chcp 65001 >nul
echo 🐳 بدء تشغيل موقع موريباك باستخدام Docker...

REM التحقق من وجود Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker غير مثبت. يرجى تثبيت Docker أولاً.
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً.
    pause
    exit /b 1
)

REM إيقاف الحاويات السابقة إن وجدت
echo 🛑 إيقاف الحاويات السابقة...
docker-compose down

REM بناء وتشغيل الحاويات
echo 🔨 بناء الحاويات...
docker-compose build

echo 🚀 تشغيل الحاويات...
docker-compose up -d

REM انتظار تشغيل قاعدة البيانات
echo ⏳ انتظار تشغيل قاعدة البيانات...
timeout /t 30 /nobreak >nul

REM تطبيق الهجرات
echo 🔄 تطبيق هجرات قاعدة البيانات...
docker-compose exec web python manage.py migrate --settings=mauribac_results.settings_production

REM إنشاء البيانات التجريبية
echo 📊 إنشاء البيانات التجريبية...
docker-compose exec web python manage.py create_sample_data --settings=mauribac_results.settings_production

echo.
echo ✅ تم تشغيل الموقع بنجاح!
echo 🌐 الموقع متاح على: http://localhost
echo 🔧 لوحة الإدارة: http://localhost/admin/
echo.
echo 📋 أوامر مفيدة:
echo   - عرض السجلات: docker-compose logs -f
echo   - إيقاف الموقع: docker-compose down
echo   - إعادة التشغيل: docker-compose restart
echo   - الدخول للحاوية: docker-compose exec web bash
echo.
pause
