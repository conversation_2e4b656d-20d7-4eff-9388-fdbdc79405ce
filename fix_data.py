#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mauribac_results.settings')
django.setup()

from results.models import Competition, Subject, Wilaya, School, Student, Result

def fix_data():
    print("🔧 إصلاح البيانات...")
    
    try:
        # التأكد من وجود المسابقة
        competition, created = Competition.objects.get_or_create(
            slug='bac-2025-newResults',
            defaults={
                'name': 'باكالوريا 2025',
                'year': 2025,
                'is_active': True
            }
        )
        print(f"✅ المسابقة: {competition.name}")
        
        # التأكد من وجود الشعبة
        subject, created = Subject.objects.get_or_create(
            code='SN',
            defaults={'name': 'العلوم الطبيعية'}
        )
        print(f"✅ الشعبة: {subject.name}")
        
        # التأكد من وجود الولاية
        wilaya, created = Wilaya.objects.get_or_create(
            code='NKC',
            defaults={'name': 'انواكشوط الشمالية'}
        )
        print(f"✅ الولاية: {wilaya.name}")
        
        # التأكد من وجود المدرسة
        school, created = School.objects.get_or_create(
            name='ثانوية النجاح',
            defaults={'wilaya': wilaya}
        )
        print(f"✅ المدرسة: {school.name}")
        
        # إنشاء الطلاب والنتائج
        students_data = [
            {'number': '40001', 'first_name': 'محمد', 'last_name': 'عبد الله', 'average': 15.50},
            {'number': '40002', 'first_name': 'فاطمة', 'last_name': 'أحمد', 'average': 16.25},
            {'number': '40003', 'first_name': 'عبد الله', 'last_name': 'محمد', 'average': 14.75},
            {'number': '40004', 'first_name': 'مريم', 'last_name': 'علي', 'average': 17.00},
            {'number': '40005', 'first_name': 'أحمد', 'last_name': 'إبراهيم', 'average': 13.25},
            {'number': '40006', 'first_name': 'عائشة', 'last_name': 'يوسف', 'average': 18.75},
        ]
        
        for student_data in students_data:
            # حذف النتيجة القديمة إن وجدت
            Result.objects.filter(student_number=student_data['number']).delete()
            
            # إنشاء أو الحصول على الطالب
            student, created = Student.objects.get_or_create(
                first_name=student_data['first_name'],
                last_name=student_data['last_name'],
                defaults={'gender': 'M' if student_data['first_name'] in ['محمد', 'عبد الله', 'أحمد'] else 'F'}
            )
            
            # إنشاء النتيجة
            result = Result.objects.create(
                competition=competition,
                student=student,
                subject=subject,
                school=school,
                student_number=student_data['number'],
                average=student_data['average'],
                status='PASS' if student_data['average'] >= 10 else 'FAIL',
                rank=1
            )
            
            print(f"✅ {student_data['number']}: {student.full_name} - معدل {student_data['average']}")
        
        print(f"\n🎉 تم إصلاح البيانات!")
        print(f"📊 إجمالي النتائج: {Result.objects.count()}")
        
        # اختبار البحث
        print("\n🔍 اختبار البحث:")
        test_numbers = ['40001', '40002', '40003']
        for num in test_numbers:
            try:
                result = Result.objects.get(student_number=num)
                print(f"✅ {num}: {result.student.full_name}")
            except Result.DoesNotExist:
                print(f"❌ {num}: غير موجود")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_data()
