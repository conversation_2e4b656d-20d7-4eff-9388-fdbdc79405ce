from django.core.management.base import BaseCommand
from results.models import Result

class Command(BaseCommand):
    help = 'إصلاح نتيجة محددة'

    def handle(self, *args, **options):
        try:
            # البحث عن النتيجة برقم 30005
            result = Result.objects.get(student_number='30005')
            
            self.stdout.write(f'وجدت النتيجة:')
            self.stdout.write(f'  الطالب: {result.student.first_name} {result.student.last_name}')
            self.stdout.write(f'  المعدل: {result.average}')
            self.stdout.write(f'  الحالة الحالية: {result.status}')
            
            # تحديث الحالة يدوياً
            if result.average == 8.00:
                result.status = 'SESSION'
                result.save()
                self.stdout.write(
                    self.style.SUCCESS('تم تحديث الحالة إلى SESSION')
                )
            
        except Result.DoesNotExist:
            self.stdout.write('لم يتم العثور على النتيجة')
            
            # إنشاء نتيجة تجريبية
            from results.models import Student, Competition, Subject, School, Wilaya
            from datetime import date
            
            # الحصول على البيانات الأساسية أو إنشاؤها
            try:
                competition = Competition.objects.first()
                subject = Subject.objects.first()
                school = School.objects.first()
                
                if not all([competition, subject, school]):
                    self.stdout.write('لا توجد بيانات أساسية')
                    return
                
                # إنشاء طالب
                student = Student.objects.create(
                    first_name='طالب',
                    last_name='Session',
                    birth_date=date(2005, 1, 1)
                )
                
                # إنشاء النتيجة
                result = Result.objects.create(
                    competition=competition,
                    student=student,
                    subject=subject,
                    school=school,
                    student_number='30005',
                    average=8.00,
                    rank=1
                )
                
                self.stdout.write(
                    self.style.SUCCESS(f'تم إنشاء نتيجة جديدة - الحالة: {result.status}')
                )
                
            except Exception as e:
                self.stdout.write(f'خطأ في الإنشاء: {str(e)}')
        
        except Exception as e:
            self.stdout.write(f'خطأ عام: {str(e)}')
