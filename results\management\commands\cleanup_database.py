from django.core.management.base import BaseCommand
from results.models import Competition, Subject, Wilaya, School, Student, Result
from django.db.models import Count

class Command(BaseCommand):
    help = 'تنظيف قاعدة البيانات من البيانات المكررة والمشاكل'

    def handle(self, *args, **options):
        self.stdout.write('بدء تنظيف قاعدة البيانات...')
        
        # 1. تنظيف الطلاب المكررين
        self.cleanup_duplicate_students()
        
        # 2. تنظيف النتائج المكررة
        self.cleanup_duplicate_results()
        
        # 3. تنظيف المدارس المكررة
        self.cleanup_duplicate_schools()
        
        # 4. إعادة حساب الترتيب
        self.recalculate_rankings()
        
        # 5. التحقق من سلامة البيانات
        self.verify_data_integrity()
        
        self.stdout.write(
            self.style.SUCCESS('تم تنظيف قاعدة البيانات بنجاح!')
        )
    
    def cleanup_duplicate_students(self):
        """تنظيف الطلاب المكررين"""
        self.stdout.write('تنظيف الطلاب المكررين...')
        
        # البحث عن الطلاب المكررين (نفس الاسم الأول والأخير)
        duplicates = Student.objects.values(
            'first_name', 'last_name'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=1)
        
        removed_count = 0
        for duplicate in duplicates:
            students = Student.objects.filter(
                first_name=duplicate['first_name'],
                last_name=duplicate['last_name']
            ).order_by('id')
            
            # الاحتفاظ بالطالب الأول وحذف الباقي
            main_student = students.first()
            duplicate_students = students[1:]
            
            for dup_student in duplicate_students:
                # نقل النتائج للطالب الرئيسي
                Result.objects.filter(student=dup_student).update(student=main_student)
                dup_student.delete()
                removed_count += 1
        
        self.stdout.write(f'✅ تم حذف {removed_count} طالب مكرر')
    
    def cleanup_duplicate_results(self):
        """تنظيف النتائج المكررة"""
        self.stdout.write('تنظيف النتائج المكررة...')
        
        # البحث عن النتائج المكررة (نفس الطالب والمسابقة)
        duplicates = Result.objects.values(
            'student', 'competition'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=1)
        
        removed_count = 0
        for duplicate in duplicates:
            results = Result.objects.filter(
                student_id=duplicate['student'],
                competition_id=duplicate['competition']
            ).order_by('-average', 'id')  # الاحتفاظ بأفضل نتيجة
            
            # حذف النتائج المكررة (الاحتفاظ بالأولى)
            duplicate_results = results[1:]
            for result in duplicate_results:
                result.delete()
                removed_count += 1
        
        self.stdout.write(f'✅ تم حذف {removed_count} نتيجة مكررة')
    
    def cleanup_duplicate_schools(self):
        """تنظيف المدارس المكررة"""
        self.stdout.write('تنظيف المدارس المكررة...')
        
        # البحث عن المدارس المكررة (نفس الاسم والولاية)
        duplicates = School.objects.values(
            'name', 'wilaya'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=1)
        
        removed_count = 0
        for duplicate in duplicates:
            schools = School.objects.filter(
                name=duplicate['name'],
                wilaya_id=duplicate['wilaya']
            ).order_by('id')
            
            # الاحتفاظ بالمدرسة الأولى وحذف الباقي
            main_school = schools.first()
            duplicate_schools = schools[1:]
            
            for dup_school in duplicate_schools:
                # نقل النتائج للمدرسة الرئيسية
                Result.objects.filter(school=dup_school).update(school=main_school)
                dup_school.delete()
                removed_count += 1
        
        self.stdout.write(f'✅ تم حذف {removed_count} مدرسة مكررة')
    
    def recalculate_rankings(self):
        """إعادة حساب الترتيب لجميع المسابقات"""
        self.stdout.write('إعادة حساب الترتيب...')
        
        competitions = Competition.objects.all()
        
        for competition in competitions:
            # إعادة حساب الترتيب العام
            results = Result.objects.filter(
                competition=competition
            ).order_by('-average', 'student__first_name')
            
            for rank, result in enumerate(results, 1):
                result.rank = rank
                result.save(update_fields=['rank'])
            
            # إعادة حساب الترتيب لكل شعبة
            subjects = Subject.objects.filter(
                result__competition=competition
            ).distinct()
            
            for subject in subjects:
                subject_results = Result.objects.filter(
                    competition=competition,
                    subject=subject
                ).order_by('-average', 'student__first_name')
                
                for rank, result in enumerate(subject_results, 1):
                    # يمكن إضافة حقل subject_rank إذا كان مطلوباً
                    pass
        
        self.stdout.write('✅ تم إعادة حساب الترتيب')
    
    def verify_data_integrity(self):
        """التحقق من سلامة البيانات"""
        self.stdout.write('التحقق من سلامة البيانات...')
        
        # التحقق من النتائج بدون طلاب
        orphaned_results = Result.objects.filter(student__isnull=True).count()
        if orphaned_results > 0:
            self.stdout.write(f'⚠️ يوجد {orphaned_results} نتيجة بدون طالب')
        
        # التحقق من النتائج بدون مدارس
        no_school_results = Result.objects.filter(school__isnull=True).count()
        if no_school_results > 0:
            self.stdout.write(f'⚠️ يوجد {no_school_results} نتيجة بدون مدرسة')
        
        # التحقق من المعدلات غير المنطقية
        invalid_averages = Result.objects.filter(
            average__lt=0
        ).count() + Result.objects.filter(
            average__gt=20
        ).count()
        
        if invalid_averages > 0:
            self.stdout.write(f'⚠️ يوجد {invalid_averages} معدل غير منطقي')
        
        # إحصائيات نهائية
        total_competitions = Competition.objects.count()
        total_students = Student.objects.count()
        total_results = Result.objects.count()
        total_schools = School.objects.count()
        
        self.stdout.write('📊 إحصائيات قاعدة البيانات:')
        self.stdout.write(f'   - المسابقات: {total_competitions}')
        self.stdout.write(f'   - الطلاب: {total_students}')
        self.stdout.write(f'   - النتائج: {total_results}')
        self.stdout.write(f'   - المدارس: {total_schools}')
        
        self.stdout.write('✅ تم التحقق من سلامة البيانات')
