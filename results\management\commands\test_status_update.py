from django.core.management.base import BaseCommand
from results.models import Result

class Command(BaseCommand):
    help = 'اختبار تحديث حالة نتيجة واحدة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--student-number',
            type=str,
            help='رقم الطالب المراد تحديث حالته',
            default='30005'
        )

    def handle(self, *args, **options):
        student_number = options['student_number']
        
        try:
            result = Result.objects.get(student_number=student_number)
            
            self.stdout.write(f'الطالب: {result.student.first_name} {result.student.last_name}')
            self.stdout.write(f'رقم الطالب: {result.student_number}')
            self.stdout.write(f'المعدل: {result.average}')
            self.stdout.write(f'الحالة الحالية: {result.status}')
            
            # تحديث الحالة بناءً على المعدل
            if result.average is not None:
                new_status = Result.get_status_from_average(result.average)
                self.stdout.write(f'الحالة المتوقعة: {new_status}')
                
                if result.status != new_status:
                    result.status = new_status
                    result.save()
                    self.stdout.write(
                        self.style.SUCCESS(f'تم تحديث الحالة من {result.status} إلى {new_status}')
                    )
                else:
                    self.stdout.write('الحالة صحيحة بالفعل')
            
            # عرض تفسير الحالات
            self.stdout.write('\n📋 تفسير الحالات:')
            self.stdout.write('   - 0.00 - 7.99: راسب (FAIL)')
            self.stdout.write('   - 8.00 - 9.99: Session (SESSION)')
            self.stdout.write('   - 10.00+: ناجح (PASS)')
            
        except Result.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'لم يتم العثور على طالب برقم: {student_number}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ: {str(e)}')
            )
