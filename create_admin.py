#!/usr/bin/env python
"""
سكريبت لإنشاء مستخدم إداري لموقع موريباك
"""
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mauribac_results.settings')
django.setup()

from django.contrib.auth.models import User

def create_admin_user():
    """إنشاء مستخدم إداري"""
    
    # بيانات المستخدم الإداري الافتراضية
    username = 'admin'
    email = '<EMAIL>'
    password = 'mauribac2024'
    
    # التحقق من وجود المستخدم
    if User.objects.filter(username=username).exists():
        print(f"✅ المستخدم الإداري '{username}' موجود بالفعل")
        user = User.objects.get(username=username)
    else:
        # إنشاء المستخدم الإداري
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )
        print(f"✅ تم إنشاء المستخدم الإداري '{username}' بنجاح")
    
    print("\n📋 بيانات تسجيل الدخول:")
    print(f"🔗 رابط لوحة الإدارة: http://127.0.0.1:8000/admin/")
    print(f"👤 اسم المستخدم: {username}")
    print(f"🔑 كلمة المرور: {password}")
    print(f"📧 البريد الإلكتروني: {email}")
    
    return user

if __name__ == "__main__":
    print("🔧 إنشاء مستخدم إداري لموقع موريباك...")
    try:
        create_admin_user()
        print("\n🎉 تم الإعداد بنجاح!")
        print("يمكنك الآن تسجيل الدخول إلى لوحة الإدارة")
    except Exception as e:
        print(f"❌ خطأ: {e}")
