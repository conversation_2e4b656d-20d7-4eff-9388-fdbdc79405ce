from django.core.management.base import BaseCommand
from results.models import Wilaya, School, Subject, Competition, Student, Result

class Command(BaseCommand):
    help = 'إنشاء بيانات بسيطة للاختبار'

    def handle(self, *args, **options):
        self.stdout.write('إنشاء بيانات بسيطة...')
        
        try:
            # إنشاء ولاية واحدة
            wilaya = Wilaya.objects.create(code='16', name='الجزائر')
            self.stdout.write('✅ تم إنشاء الولاية')
            
            # إنشاء مدرسة واحدة
            school = School.objects.create(name='ثانوية الجزائر', wilaya=wilaya)
            self.stdout.write('✅ تم إنشاء المدرسة')
            
            # إنشاء شعبة واحدة
            subject = Subject.objects.create(code='SM', name='علوم تجريبية')
            self.stdout.write('✅ تم إنشاء الشعبة')
            
            # إنشاء مسابقة واحدة
            competition = Competition.objects.create(
                name='بكالوريا 2024',
                year=2024,
                slug='bac-2024',
                is_active=True
            )
            self.stdout.write('✅ تم إنشاء المسابقة')
            
            # إنشاء 10 طلاب مع نتائج
            for i in range(10):
                student = Student.objects.create(
                    student_number=f'2024{i+1:03d}',
                    first_name=f'طالب{i+1}',
                    last_name='اختبار',
                    gender='M'
                )
                
                # إنشاء نتيجة
                if i < 6:  # 6 ناجح
                    average = 15.0
                    status = 'PASS'
                elif i < 8:  # 2 Session
                    average = 8.5
                    status = 'SESSION'
                else:  # 2 راسب
                    average = 6.0
                    status = 'FAIL'
                
                Result.objects.create(
                    competition=competition,
                    student=student,
                    subject=subject,
                    school=school,
                    student_number=student.student_number,
                    average=average,
                    status=status,
                    rank=i+1
                )
            
            self.stdout.write('✅ تم إنشاء 10 طلاب مع نتائجهم')
            
            # عرض الإحصائيات
            self.stdout.write(f'المسابقات: {Competition.objects.count()}')
            self.stdout.write(f'الطلاب: {Student.objects.count()}')
            self.stdout.write(f'النتائج: {Result.objects.count()}')
            self.stdout.write(f'المدارس: {School.objects.count()}')
            
            self.stdout.write(self.style.SUCCESS('تم بنجاح!'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'خطأ: {str(e)}'))
