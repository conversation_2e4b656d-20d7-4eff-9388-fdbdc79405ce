"""
URL configuration for mauribac_results project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from results.admin_views import import_excel_view, download_excel_template, delete_competition_view, delete_competition_confirm
from results.admin import admin_site

urlpatterns = [
    path('admin/import-excel/', import_excel_view, name='import_excel'),
    path('admin/download-template/', download_excel_template, name='download_template'),
    path('admin/delete-competition/', delete_competition_view, name='delete_competition'),
    path('admin/delete-competition/<int:competition_id>/', delete_competition_confirm, name='delete_competition_confirm'),
    path('admin/', admin_site.urls),
    path('', include('results.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
