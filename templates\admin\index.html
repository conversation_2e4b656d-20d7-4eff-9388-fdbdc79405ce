{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}لوحة الإدارة | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>مرحباً بك في لوحة إدارة Expert Net</h1>
    
    <!-- أدوات سريعة -->
    <div class="quick-tools">
        <h2>🛠️ أدوات سريعة</h2>
        <div class="tools-grid">
            <a href="/admin/import-excel/" class="tool-card">
                <div class="tool-icon">📊</div>
                <h3>استيراد من Excel</h3>
                <p>استيراد نتائج جديدة من ملف Excel</p>
            </a>
            
            <a href="/admin/download-template/" class="tool-card">
                <div class="tool-icon">📥</div>
                <h3>تحميل قالب Excel</h3>
                <p>تحميل قالب Excel للتعبئة</p>
            </a>
            
            <a href="/admin/delete-competition/" class="tool-card danger">
                <div class="tool-icon">🗑️</div>
                <h3>حذف المسابقات</h3>
                <p>حذف المسابقات والنتائج المرتبطة بها</p>
            </a>
            
            <a href="/" target="_blank" class="tool-card">
                <div class="tool-icon">🌐</div>
                <h3>عرض الموقع</h3>
                <p>مشاهدة الموقع الرئيسي</p>
            </a>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="stats-section">
        <h2>📈 إحصائيات سريعة</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ competitions_count|default:0 }}</div>
                <div class="stat-label">المسابقات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ results_count|default:0 }}</div>
                <div class="stat-label">النتائج</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ students_count|default:0 }}</div>
                <div class="stat-label">الطلاب</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ schools_count|default:0 }}</div>
                <div class="stat-label">المدارس</div>
            </div>
        </div>
    </div>
</div>

<!-- النمط الأصلي للإدارة -->
{{ block.super }}

<style>
.dashboard {
    margin: 20px 0;
}

.dashboard h1 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.quick-tools, .stats-section {
    margin-bottom: 40px;
}

.quick-tools h2, .stats-section h2 {
    color: #666;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.tool-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
    color: #333;
}

.tool-card.danger {
    border-color: #dc3545;
}

.tool-card.danger:hover {
    background: #fff5f5;
}

.tool-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.tool-card h3 {
    margin: 10px 0;
    color: #333;
    font-size: 18px;
}

.tool-card p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 30px 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 16px;
    opacity: 0.9;
}

/* تحسين التصميم للشاشات الصغيرة */
@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tool-icon {
        font-size: 36px;
    }
    
    .stat-number {
        font-size: 28px;
    }
}
</style>
{% endblock %}
