{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}لوحة الإدارة | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>مرحباً بك في لوحة إدارة Expert Net</h1>
    
    <!-- أدوات سريعة -->
    <div class="quick-tools">
        <h2>🛠️ أدوات سريعة</h2>
        <div class="tools-grid">
            <a href="/admin/import-excel/" class="tool-card">
                <div class="tool-icon">📊</div>
                <h3>استيراد من Excel</h3>
                <p>استيراد نتائج جديدة من ملف Excel</p>
            </a>
            
            <a href="/admin/download-template/" class="tool-card">
                <div class="tool-icon">📥</div>
                <h3>تحميل قالب Excel</h3>
                <p>تحميل قالب Excel للتعبئة</p>
            </a>
            
            <a href="/admin/delete-competition/" class="tool-card danger">
                <div class="tool-icon">🗑️</div>
                <h3>حذف المسابقات</h3>
                <p>حذف المسابقات والنتائج المرتبطة بها</p>
            </a>
            
            <a href="/" target="_blank" class="tool-card">
                <div class="tool-icon">🌐</div>
                <h3>عرض الموقع</h3>
                <p>مشاهدة الموقع الرئيسي</p>
            </a>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="stats-section">
        <h2>📈 إحصائيات عامة</h2>
        <div class="stats-grid">
            <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="stat-number">{{ schools_count|default:0 }}</div>
                <div class="stat-label">المدارس</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="stat-number">{{ students_count|default:0 }}</div>
                <div class="stat-label">الطلاب</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="stat-number">{{ results_count|default:0 }}</div>
                <div class="stat-label">النتائج</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="stat-number">{{ competitions_count|default:0 }}</div>
                <div class="stat-label">المسابقات</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div class="stat-number">{{ subjects_count|default:0 }}</div>
                <div class="stat-label">الشعب</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                <div class="stat-number">{{ wilayas_count|default:0 }}</div>
                <div class="stat-label">الولايات</div>
            </div>
        </div>
    </div>

    <!-- إحصائيات النتائج المفصلة -->
    {% if results_stats %}
    <div class="detailed-stats-section">
        <h2>📊 إحصائيات النتائج التفصيلية</h2>
        <div class="detailed-stats-grid">
            <div class="detailed-stat-card success">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <div class="stat-number">{{ results_stats.passed_count }}</div>
                    <div class="stat-label">ناجح</div>
                    <div class="stat-percentage">{{ results_stats.pass_rate }}%</div>
                </div>
            </div>
            <div class="detailed-stat-card warning">
                <div class="stat-icon">⚠️</div>
                <div class="stat-info">
                    <div class="stat-number">{{ results_stats.session_count }}</div>
                    <div class="stat-label">Session</div>
                    <div class="stat-percentage">{{ results_stats.session_rate }}%</div>
                </div>
            </div>
            <div class="detailed-stat-card danger">
                <div class="stat-icon">❌</div>
                <div class="stat-info">
                    <div class="stat-number">{{ results_stats.failed_count }}</div>
                    <div class="stat-label">راسب</div>
                    <div class="stat-percentage">{{ results_stats.fail_rate }}%</div>
                </div>
            </div>
            <div class="detailed-stat-card secondary">
                <div class="stat-icon">👻</div>
                <div class="stat-info">
                    <div class="stat-number">{{ results_stats.absent_count }}</div>
                    <div class="stat-label">غائب</div>
                    <div class="stat-percentage">{{ results_stats.absent_rate }}%</div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- إحصائيات المسابقة النشطة -->
    {% if active_competition_stats %}
    <div class="active-competition-section">
        <h2>🏆 {{ active_competition_stats.name }} ({{ active_competition_stats.year }})</h2>
        <div class="competition-stats-grid">
            <div class="competition-stat-card">
                <div class="stat-number">{{ active_competition_stats.total_students }}</div>
                <div class="stat-label">إجمالي الطلاب</div>
            </div>
            <div class="competition-stat-card success">
                <div class="stat-number">{{ active_competition_stats.passed_students }}</div>
                <div class="stat-label">ناجح</div>
            </div>
            <div class="competition-stat-card warning">
                <div class="stat-number">{{ active_competition_stats.session_students }}</div>
                <div class="stat-label">Session</div>
            </div>
            <div class="competition-stat-card danger">
                <div class="stat-number">{{ active_competition_stats.failed_students }}</div>
                <div class="stat-label">راسب</div>
            </div>
            <div class="competition-stat-card info">
                <div class="stat-number">{{ active_competition_stats.success_rate }}%</div>
                <div class="stat-label">معدل النجاح</div>
            </div>
            <div class="competition-stat-card primary">
                <div class="stat-number">{{ active_competition_stats.average_score }}</div>
                <div class="stat-label">المعدل العام</div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- أفضل الشعب -->
    {% if top_subjects %}
    <div class="top-subjects-section">
        <h2>📚 أفضل الشعب (معدل النجاح)</h2>
        <div class="subjects-grid">
            {% for subject in top_subjects %}
            <div class="subject-card">
                <div class="subject-name">{{ subject.name }}</div>
                <div class="subject-stats">
                    <div class="subject-rate">{{ subject.pass_rate }}%</div>
                    <div class="subject-count">{{ subject.passed_students }}/{{ subject.total_students }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- أفضل الولايات -->
    {% if top_wilayas %}
    <div class="top-wilayas-section">
        <h2>🌍 أفضل الولايات (معدل النجاح)</h2>
        <div class="wilayas-grid">
            {% for wilaya in top_wilayas %}
            <div class="wilaya-card">
                <div class="wilaya-name">{{ wilaya.name }}</div>
                <div class="wilaya-stats">
                    <div class="wilaya-rate">{{ wilaya.pass_rate }}%</div>
                    <div class="wilaya-count">{{ wilaya.passed_students }}/{{ wilaya.total_students }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- النمط الأصلي للإدارة -->
{{ block.super }}

<style>
.dashboard {
    margin: 20px 0;
}

.dashboard h1 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.quick-tools, .stats-section {
    margin-bottom: 40px;
}

.quick-tools h2, .stats-section h2 {
    color: #666;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.tool-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
    color: #333;
}

.tool-card.danger {
    border-color: #dc3545;
}

.tool-card.danger:hover {
    background: #fff5f5;
}

.tool-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.tool-card h3 {
    margin: 10px 0;
    color: #333;
    font-size: 18px;
}

.tool-card p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    color: white;
    padding: 30px 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 16px;
    opacity: 0.9;
}

/* إحصائيات النتائج المفصلة */
.detailed-stats-section, .active-competition-section, .top-subjects-section, .top-wilayas-section {
    margin-bottom: 40px;
}

.detailed-stats-section h2, .active-competition-section h2, .top-subjects-section h2, .top-wilayas-section h2 {
    color: #666;
    border-bottom: 2px solid #28a745;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.detailed-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detailed-stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.detailed-stat-card:hover {
    transform: translateY(-2px);
}

.detailed-stat-card.success {
    border-left: 5px solid #28a745;
}

.detailed-stat-card.warning {
    border-left: 5px solid #ffc107;
}

.detailed-stat-card.danger {
    border-left: 5px solid #dc3545;
}

.detailed-stat-card.secondary {
    border-left: 5px solid #6c757d;
}

.stat-icon {
    font-size: 2rem;
    margin-right: 15px;
}

.stat-info {
    flex: 1;
}

.stat-info .stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-info .stat-label {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.stat-info .stat-percentage {
    color: #007bff;
    font-weight: bold;
    font-size: 0.8rem;
}

/* إحصائيات المسابقة النشطة */
.competition-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.competition-stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-top: 4px solid #007bff;
}

.competition-stat-card.success {
    border-top-color: #28a745;
}

.competition-stat-card.warning {
    border-top-color: #ffc107;
}

.competition-stat-card.danger {
    border-top-color: #dc3545;
}

.competition-stat-card.info {
    border-top-color: #17a2b8;
}

.competition-stat-card.primary {
    border-top-color: #007bff;
}

/* أفضل الشعب */
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.subject-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.subject-card:hover {
    transform: translateY(-2px);
}

.subject-name {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.subject-rate {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.subject-count {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* أفضل الولايات */
.wilayas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.wilaya-card {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.wilaya-card:hover {
    transform: translateY(-2px);
}

.wilaya-name {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.wilaya-rate {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.wilaya-count {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* تحسين التصميم للشاشات الصغيرة */
@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .detailed-stats-grid {
        grid-template-columns: 1fr;
    }

    .competition-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .subjects-grid, .wilayas-grid {
        grid-template-columns: 1fr;
    }

    .tool-icon {
        font-size: 36px;
    }

    .stat-number {
        font-size: 28px;
    }
}
</style>
{% endblock %}
