version: '3.8'

services:
  # خدمة قاعدة البيانات MySQL
  db:
    image: mysql:8.0
    container_name: mauribac_db
    restart: always
    environment:
      MYSQL_DATABASE: mauribac_results
      MYSQL_USER: mauribac_user
      MYSQL_PASSWORD: mauribac_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./setup_database.sql:/docker-entrypoint-initdb.d/setup_database.sql
    ports:
      - "3306:3306"
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # خدمة التطبيق Django
  web:
    build: .
    container_name: mauribac_web
    restart: always
    environment:
      DB_NAME: mauribac_results
      DB_USER: mauribac_user
      DB_PASSWORD: mauribac_password
      DB_HOST: db
      DB_PORT: 3306
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - logs_volume:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - db
    command: >
      sh -c "
        python manage.py migrate --settings=mauribac_results.settings_production &&
        python manage.py create_sample_data --settings=mauribac_results.settings_production &&
        python manage.py runserver 0.0.0.0:8000 --settings=mauribac_results.settings_production
      "

  # خدمة Nginx (اختيارية للإنتاج)
  nginx:
    image: nginx:alpine
    container_name: mauribac_nginx
    restart: always
    ports:
      - "80:80"
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - web

volumes:
  mysql_data:
  static_volume:
  media_volume:
  logs_volume:
