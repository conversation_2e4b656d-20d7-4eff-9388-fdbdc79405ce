#!/usr/bin/env python
"""
سكريبت لتشغيل الخادم بسرعة مع إعداد قاعدة البيانات
"""
import os
import sys
import subprocess

def run_command(command, description):
    """تشغيل أمر مع عرض الوصف"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - تم بنجاح")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في {description}")
        if e.stderr:
            print(f"خطأ: {e.stderr}")
        if e.stdout:
            print(f"إخراج: {e.stdout}")
        return False

def main():
    print("🚀 بدء تشغيل موقع موريباك...")

    # التحقق من وجود Python
    if not run_command("python --version", "التحقق من Python"):
        print("❌ Python غير مثبت أو غير متاح في PATH")
        return

    # تثبيت المتطلبات
    if not run_command("pip install django", "تثبيت Django"):
        print("⚠️ تحذير: فشل في تثبيت Django")

    # إنشاء الهجرات
    if not run_command("python manage.py makemigrations results --settings=mauribac_results.settings_sqlite", "إنشاء الهجرات"):
        print("❌ فشل في إنشاء الهجرات")
        return

    # تطبيق الهجرات
    if not run_command("python manage.py migrate --settings=mauribac_results.settings_sqlite", "تطبيق الهجرات"):
        print("❌ فشل في تطبيق الهجرات")
        return

    # إنشاء البيانات التجريبية
    print("\n📊 إنشاء البيانات التجريبية...")
    if run_command("python manage.py create_sample_data --settings=mauribac_results.settings_sqlite", "إنشاء البيانات التجريبية"):
        print("✅ تم إنشاء البيانات التجريبية بنجاح")
    else:
        print("⚠️ تحذير: فشل في إنشاء البيانات التجريبية")

    # تشغيل الخادم
    print("\n🌐 تشغيل خادم Django...")
    print("📍 الموقع سيكون متاحاً على: http://127.0.0.1:8000/")
    print("🔧 لوحة الإدارة متاحة على: http://127.0.0.1:8000/admin/")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")

    try:
        subprocess.run("python manage.py runserver --settings=mauribac_results.settings_sqlite", shell=True, check=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم بنجاح")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()
