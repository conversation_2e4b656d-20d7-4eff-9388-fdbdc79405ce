@echo off
chcp 65001 >nul
echo 🚀 إعداد MySQL لموقع موريباك...

echo.
echo 📋 خطوات الإعداد:
echo 1. التحقق من MySQL
echo 2. إنشاء قاعدة البيانات
echo 3. تطبيق الهجرات
echo 4. إنشاء البيانات التجريبية
echo 5. تشغيل الموقع
echo.

REM التحقق من MySQL
echo 🔍 التحقق من MySQL...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL غير مثبت أو غير متاح
    echo يرجى تثبيت MySQL من: https://dev.mysql.com/downloads/mysql/
    pause
    exit /b 1
)
echo ✅ MySQL متاح

REM إنشاء قاعدة البيانات
echo.
echo 📊 إنشاء قاعدة البيانات...
echo أدخل كلمة مرور MySQL (root):
set /p mysql_password=

REM إنشاء ملف SQL مؤقت
echo CREATE DATABASE IF NOT EXISTS mauribac_results CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; > temp_db.sql
echo USE mauribac_results; >> temp_db.sql
echo SELECT 'تم إنشاء قاعدة البيانات بنجاح!' AS message; >> temp_db.sql

REM تشغيل MySQL
if "%mysql_password%"=="" (
    mysql -u root < temp_db.sql
) else (
    mysql -u root -p%mysql_password% < temp_db.sql
)

if errorlevel 1 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo تأكد من:
    echo - تشغيل خدمة MySQL
    echo - صحة كلمة المرور
    echo - صلاحيات إنشاء قواعد البيانات
    del temp_db.sql
    pause
    exit /b 1
)

del temp_db.sql
echo ✅ تم إنشاء قاعدة البيانات بنجاح

REM تثبيت المتطلبات
echo.
echo 📦 تثبيت المتطلبات...
pip install mysqlclient
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت mysqlclient
    echo قد تحتاج لتثبيته يدوياً
)

REM إنشاء الهجرات
echo.
echo 🔄 إنشاء الهجرات...
python manage.py makemigrations
if errorlevel 1 (
    echo ❌ فشل في إنشاء الهجرات
    pause
    exit /b 1
)

REM تطبيق الهجرات
echo.
echo 🔄 تطبيق الهجرات...
python manage.py migrate
if errorlevel 1 (
    echo ❌ فشل في تطبيق الهجرات
    pause
    exit /b 1
)

REM إنشاء البيانات التجريبية
echo.
echo 📊 إنشاء البيانات التجريبية...
python manage.py create_sample_data
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في إنشاء البيانات التجريبية
) else (
    echo ✅ تم إنشاء البيانات التجريبية بنجاح
)

echo.
echo 🎉 تم إعداد MySQL بنجاح!
echo 📍 يمكنك الآن تشغيل الموقع باستخدام:
echo python manage.py runserver
echo.
echo 🌐 الموقع سيكون متاحاً على: http://127.0.0.1:8000/
echo.

REM سؤال عن تشغيل الخادم
set /p start_server=هل تريد تشغيل الخادم الآن؟ (y/n): 
if /i "%start_server%"=="y" goto start_server
if /i "%start_server%"=="yes" goto start_server
if /i "%start_server%"=="نعم" goto start_server
goto end

:start_server
echo.
echo 🌐 تشغيل خادم Django...
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo.
python manage.py runserver

:end
pause
