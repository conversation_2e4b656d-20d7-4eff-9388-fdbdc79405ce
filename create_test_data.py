#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mauribac_results.settings')
django.setup()

from results.models import Competition, Subject, Wilaya, School, Student, Result

def create_test_data():
    print("إنشاء بيانات تجريبية...")
    
    # إنشاء مسابقة
    competition, created = Competition.objects.get_or_create(
        slug='bac-2025-test',
        defaults={
            'name': 'باكالوريا 2025 - اختبار',
            'year': 2025,
            'is_active': True
        }
    )
    print(f"المسابقة: {competition.name} ({'تم إنشاؤها' if created else 'موجودة مسبقاً'})")
    
    # إنشاء شعبة
    subject, created = Subject.objects.get_or_create(
        code='SN',
        defaults={'name': 'العلوم الطبيعية'}
    )
    print(f"الشعبة: {subject.name} ({'تم إنشاؤها' if created else 'موجودة مسبقاً'})")
    
    # إنشاء ولاية
    wilaya, created = Wilaya.objects.get_or_create(
        code='NKC',
        defaults={'name': 'انواكشوط الشمالية'}
    )
    print(f"الولاية: {wilaya.name} ({'تم إنشاؤها' if created else 'موجودة مسبقاً'})")
    
    # إنشاء مدرسة
    school, created = School.objects.get_or_create(
        name='ثانوية الاختبار',
        defaults={'wilaya': wilaya}
    )
    print(f"المدرسة: {school.name} ({'تم إنشاؤها' if created else 'موجودة مسبقاً'})")
    
    # إنشاء طلاب ونتائج تجريبية
    test_students = [
        {'number': '40001', 'first_name': 'محمد', 'last_name': 'عبد الله', 'average': 15.50},
        {'number': '40002', 'first_name': 'فاطمة', 'last_name': 'أحمد', 'average': 16.25},
        {'number': '40003', 'first_name': 'عبد الله', 'last_name': 'محمد', 'average': 14.75},
        {'number': '40004', 'first_name': 'مريم', 'last_name': 'علي', 'average': 17.00},
        {'number': '40005', 'first_name': 'أحمد', 'last_name': 'إبراهيم', 'average': 13.25},
    ]
    
    for student_data in test_students:
        # إنشاء الطالب
        student, created = Student.objects.get_or_create(
            first_name=student_data['first_name'],
            last_name=student_data['last_name'],
            defaults={}
        )
        
        # إنشاء النتيجة
        result, created = Result.objects.get_or_create(
            competition=competition,
            student=student,
            subject=subject,
            school=school,
            student_number=student_data['number'],
            defaults={
                'average': student_data['average'],
                'status': 'PASS' if student_data['average'] >= 10 else 'FAIL',
                'rank': None
            }
        )
        
        print(f"الطالب: {student.first_name} {student.last_name} - رقم {student_data['number']} - معدل {student_data['average']} ({'تم إنشاؤه' if created else 'موجود مسبقاً'})")
    
    print(f"\nإجمالي النتائج في قاعدة البيانات: {Result.objects.count()}")
    print("تم إنشاء البيانات التجريبية بنجاح!")

if __name__ == '__main__':
    create_test_data()
