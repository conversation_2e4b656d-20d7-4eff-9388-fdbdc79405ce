from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from .models import Wilaya, School, Subject, Competition, Student, Result
from decimal import Decimal


class ModelsTestCase(TestCase):
    """اختبار نماذج قاعدة البيانات"""
    
    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.wilaya = Wilaya.objects.create(
            name="انواكشوط 1 (الشمالية)",
            code="NKC1"
        )
        
        self.school = School.objects.create(
            name="المواهب الجديدة",
            wilaya=self.wilaya
        )
        
        self.subject = Subject.objects.create(
            name="العلوم الطبيعية",
            code="sn"
        )
        
        self.competition = Competition.objects.create(
            name="الباكلوريا 2024",
            year=2024,
            slug="bac-2024-test"
        )
        
        self.student = Student.objects.create(
            student_number="12345",
            first_name="محمد",
            last_name="أحمد",
            gender="M"
        )
        
        self.result = Result.objects.create(
            student=self.student,
            competition=self.competition,
            subject=self.subject,
            school=self.school,
            student_number="12345",
            average=Decimal("15.50"),
            rank=1,
            rank_in_subject=1,
            status="PASS"
        )
    
    def test_wilaya_creation(self):
        """اختبار إنشاء الولاية"""
        self.assertEqual(self.wilaya.name, "انواكشوط 1 (الشمالية)")
        self.assertEqual(str(self.wilaya), "انواكشوط 1 (الشمالية)")
    
    def test_school_creation(self):
        """اختبار إنشاء المدرسة"""
        self.assertEqual(self.school.name, "المواهب الجديدة")
        self.assertEqual(self.school.wilaya, self.wilaya)
        self.assertEqual(str(self.school), "المواهب الجديدة")
    
    def test_student_full_name(self):
        """اختبار الاسم الكامل للطالب"""
        self.assertEqual(self.student.full_name, "محمد أحمد")
    
    def test_result_creation(self):
        """اختبار إنشاء النتيجة"""
        self.assertEqual(self.result.average, Decimal("15.50"))
        self.assertEqual(self.result.rank, 1)
        self.assertEqual(self.result.status, "PASS")


class ViewsTestCase(TestCase):
    """اختبار العروض (Views)"""
    
    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.client = Client()
        
        # إنشاء البيانات الأساسية
        self.wilaya = Wilaya.objects.create(
            name="انواكشوط 1 (الشمالية)",
            code="NKC1"
        )
        
        self.school = School.objects.create(
            name="المواهب الجديدة",
            wilaya=self.wilaya
        )
        
        self.subject = Subject.objects.create(
            name="العلوم الطبيعية",
            code="sn"
        )
        
        self.competition = Competition.objects.create(
            name="الباكلوريا 2024",
            year=2024,
            slug="bac-2024-test"
        )
        
        self.student = Student.objects.create(
            student_number="12345",
            first_name="محمد",
            last_name="أحمد",
            gender="M"
        )
        
        self.result = Result.objects.create(
            student=self.student,
            competition=self.competition,
            subject=self.subject,
            school=self.school,
            student_number="12345",
            average=Decimal("15.50"),
            rank=1,
            rank_in_subject=1,
            status="PASS"
        )
    
    def test_home_view(self):
        """اختبار الصفحة الرئيسية"""
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "موريباك")
        self.assertContains(response, self.competition.name)
    
    def test_competition_detail_view(self):
        """اختبار صفحة تفاصيل المسابقة"""
        response = self.client.get(
            reverse('competition_detail', kwargs={'slug': self.competition.slug})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.competition.name)
        self.assertContains(response, "أدخل رقم الطالب")
    
    def test_search_functionality(self):
        """اختبار وظيفة البحث"""
        response = self.client.get(
            reverse('competition_detail', kwargs={'slug': self.competition.slug}),
            {'search': '12345'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "محمد أحمد")
        self.assertContains(response, "15.50")
    
    def test_search_not_found(self):
        """اختبار البحث عن طالب غير موجود"""
        response = self.client.get(
            reverse('competition_detail', kwargs={'slug': self.competition.slug}),
            {'search': '99999'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "لم يتم العثور على نتيجة")
    
    def test_student_result_view(self):
        """اختبار صفحة نتيجة الطالب"""
        response = self.client.get(
            reverse('student_result', kwargs={
                'slug': self.competition.slug,
                'student_number': '12345'
            })
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "محمد أحمد")
        self.assertContains(response, "15.50")
    
    def test_subject_results_view(self):
        """اختبار صفحة نتائج الشعبة"""
        response = self.client.get(
            reverse('subject_results', kwargs={
                'slug': self.competition.slug,
                'subject_code': self.subject.code
            })
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.subject.name)
        self.assertContains(response, "محمد أحمد")
    
    def test_school_results_view(self):
        """اختبار صفحة نتائج المدرسة"""
        response = self.client.get(
            reverse('school_results', kwargs={
                'slug': self.competition.slug,
                'subject_code': self.subject.code,
                'school_name': self.school.name
            })
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.school.name)
        self.assertContains(response, "محمد أحمد")
    
    def test_wilaya_results_view(self):
        """اختبار صفحة نتائج الولاية"""
        response = self.client.get(
            reverse('wilaya_results', kwargs={
                'slug': self.competition.slug,
                'subject_code': self.subject.code,
                'wilaya_name': self.wilaya.name
            })
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.wilaya.name)
        self.assertContains(response, "محمد أحمد")


class AdminTestCase(TestCase):
    """اختبار لوحة الإدارة"""
    
    def setUp(self):
        """إعداد مستخدم إداري"""
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client = Client()
    
    def test_admin_login(self):
        """اختبار تسجيل الدخول للإدارة"""
        response = self.client.post('/admin/login/', {
            'username': 'admin',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)  # إعادة توجيه بعد النجاح
    
    def test_admin_access(self):
        """اختبار الوصول للوحة الإدارة"""
        self.client.login(username='admin', password='testpass123')
        response = self.client.get('/admin/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Django administration')
