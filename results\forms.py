from django import forms
from django.core.exceptions import ValidationError
import openpyxl
import pandas as pd
from .models import Competition, Subject, Wilaya, School


class ExcelImportForm(forms.Form):
    """نموذج استيراد ملف Excel"""
    
    excel_file = forms.FileField(
        label="ملف Excel",
        help_text="اختر ملف Excel يحتوي على النتائج (.xlsx أو .xls)",
        widget=forms.FileInput(attrs={
            'accept': '.xlsx,.xls',
            'class': 'form-control'
        })
    )
    
    competition = forms.ModelChoiceField(
        queryset=Competition.objects.all(),
        label="المسابقة",
        help_text="اختر المسابقة التي تنتمي إليها هذه النتائج",
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )

    create_new_competition = forms.BooleanField(
        required=False,
        label="إنشاء مسابقة جديدة",
        help_text="إذا كانت محددة، سيتم إنشاء مسابقة جديدة بناءً على اسم الملف",
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

    new_competition_name = forms.CharField(
        required=False,
        max_length=200,
        label="اسم المسابقة الجديدة",
        help_text="اتركه فارغاً لاستخدام اسم الملف تلقائياً",
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: الباكلوريا 2026'})
    )

    new_competition_year = forms.IntegerField(
        required=False,
        label="سنة المسابقة",
        help_text="اتركه فارغاً لاستخدام السنة الحالية",
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '2026'})
    )
    
    overwrite_existing = forms.BooleanField(
        required=False,
        label="استبدال النتائج الموجودة",
        help_text="إذا كانت محددة، سيتم استبدال النتائج الموجودة بنفس رقم الطالب",
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    def clean_excel_file(self):
        """التحقق من صحة ملف Excel"""
        file = self.cleaned_data['excel_file']
        
        if not file.name.endswith(('.xlsx', '.xls')):
            raise ValidationError("يجب أن يكون الملف من نوع Excel (.xlsx أو .xls)")
        
        # التحقق من حجم الملف (أقل من 10 ميجابايت)
        if file.size > 10 * 1024 * 1024:
            raise ValidationError("حجم الملف كبير جداً. يجب أن يكون أقل من 10 ميجابايت")
        
        return file

    def clean(self):
        """التحقق من صحة البيانات المترابطة"""
        cleaned_data = super().clean()
        competition = cleaned_data.get('competition')
        create_new = cleaned_data.get('create_new_competition')

        if not competition and not create_new:
            raise ValidationError("يجب اختيار مسابقة موجودة أو إنشاء مسابقة جديدة")

        if competition and create_new:
            raise ValidationError("لا يمكن اختيار مسابقة موجودة وإنشاء مسابقة جديدة في نفس الوقت")

        return cleaned_data
    
    def process_excel_file(self):
        """معالجة ملف Excel واستخراج البيانات"""
        file = self.cleaned_data['excel_file']
        competition = self.cleaned_data['competition']
        
        try:
            # قراءة ملف Excel
            df = pd.read_excel(file)
            
            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['رقم_الطالب', 'الاسم_الأول', 'اسم_العائلة', 'الشعبة', 'المدرسة', 'الولاية', 'المعدل']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValidationError(f"الأعمدة التالية مفقودة في الملف: {', '.join(missing_columns)}")
            
            return df
            
        except Exception as e:
            raise ValidationError(f"خطأ في قراءة ملف Excel: {str(e)}")


class ExcelTemplateForm(forms.Form):
    """نموذج لتحميل قالب Excel"""
    
    competition = forms.ModelChoiceField(
        queryset=Competition.objects.all(),
        label="المسابقة",
        help_text="اختر المسابقة لإنشاء قالب Excel مخصص لها",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
