from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.urls import reverse
from django.db import transaction
from decimal import Decimal
from django.views.decorators.http import require_POST
import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from .forms import ExcelImportForm, ExcelTemplateForm
from .models import Competition, Subject, Wilaya, School, Student, Result
from django.utils.text import slugify
from datetime import datetime


def get_or_create_competition(form):
    """الحصول على مسابقة موجودة أو إنشاء مسابقة جديدة"""

    if form.cleaned_data['create_new_competition']:
        # إنشاء مسابقة جديدة
        file_name = form.cleaned_data['excel_file'].name
        competition_name = form.cleaned_data['new_competition_name']
        competition_year = form.cleaned_data['new_competition_year']

        # تحديد اسم المسابقة
        if not competition_name:
            # استخراج الاسم من اسم الملف
            base_name = file_name.split('.')[0]  # إزالة الامتداد
            competition_name = base_name.replace('_', ' ').replace('-', ' ')

            # إضافة كلمة "نتائج" إذا لم تكن موجودة
            if 'نتائج' not in competition_name and 'results' not in competition_name.lower():
                competition_name = f"نتائج {competition_name}"

        # تحديد السنة
        if not competition_year:
            competition_year = datetime.now().year

        # إنشاء slug فريد
        base_slug = slugify(f"{competition_name}-{competition_year}")
        slug = base_slug
        counter = 1
        while Competition.objects.filter(slug=slug).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1

        # إنشاء المسابقة
        competition = Competition.objects.create(
            name=competition_name,
            year=competition_year,
            slug=slug,
            is_active=True
        )

        return competition
    else:
        # استخدام مسابقة موجودة
        return form.cleaned_data['competition']


@staff_member_required
def import_excel_view(request):
    """صفحة استيراد ملف Excel"""
    
    if request.method == 'POST':
        form = ExcelImportForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                # معالجة ملف Excel
                df = form.process_excel_file()

                # تحديد المسابقة (موجودة أو جديدة)
                competition = get_or_create_competition(form)
                overwrite = form.cleaned_data['overwrite_existing']
                
                # إحصائيات الاستيراد
                stats = {
                    'total_rows': len(df),
                    'created': 0,
                    'updated': 0,
                    'errors': []
                }
                
                with transaction.atomic():
                    for index, row in df.iterrows():
                        try:
                            # استخراج البيانات من الصف
                            student_number = str(row['رقم_الطالب']).strip()
                            first_name = str(row['الاسم_الأول']).strip()
                            last_name = str(row['اسم_العائلة']).strip()
                            subject_name = str(row['الشعبة']).strip()
                            school_name = str(row['المدرسة']).strip()
                            wilaya_name = str(row['الولاية']).strip()
                            average = float(row['المعدل'])
                            
                            # البحث عن أو إنشاء الكائنات المطلوبة
                            subject, _ = Subject.objects.get_or_create(
                                name=subject_name,
                                defaults={'code': subject_name[:10].lower()}
                            )
                            
                            wilaya, _ = Wilaya.objects.get_or_create(
                                name=wilaya_name,
                                defaults={'code': wilaya_name[:10].upper()}
                            )
                            
                            school, _ = School.objects.get_or_create(
                                name=school_name,
                                defaults={'wilaya': wilaya}
                            )
                            
                            # إنشاء أو تحديث الطالب
                            student, _ = Student.objects.get_or_create(
                                student_number=student_number,
                                defaults={
                                    'first_name': first_name,
                                    'last_name': last_name,
                                    'gender': 'M'  # افتراضي
                                }
                            )
                            
                            # إنشاء أو تحديث النتيجة
                            result, created = Result.objects.get_or_create(
                                student_number=student_number,
                                competition=competition,
                                defaults={
                                    'student': student,
                                    'subject': subject,
                                    'school': school,
                                    'average': Decimal(str(average)),
                                    'status': 'PASS' if average >= 10 else 'FAIL'
                                }
                            )
                            
                            if not created and overwrite:
                                # تحديث النتيجة الموجودة
                                result.student = student
                                result.subject = subject
                                result.school = school
                                result.average = Decimal(str(average))
                                result.status = 'PASS' if average >= 10 else 'FAIL'
                                result.save()
                                stats['updated'] += 1
                            elif created:
                                stats['created'] += 1
                            
                        except Exception as e:
                            stats['errors'].append(f"الصف {index + 2}: {str(e)}")
                
                # تحديث الترتيب
                update_rankings(competition)
                
                # رسالة النجاح
                success_msg = f"تم استيراد {stats['created']} نتيجة جديدة"
                if stats['updated'] > 0:
                    success_msg += f" وتحديث {stats['updated']} نتيجة"

                success_msg += f" في مسابقة '{competition.name}'"

                messages.success(request, success_msg)

                # رسالة إضافية مع رابط المسابقة
                competition_url = f"http://127.0.0.1:8000/{competition.slug}/"
                messages.info(request, f"يمكنك مشاهدة النتائج على: {competition_url}")
                
                if stats['errors']:
                    for error in stats['errors'][:5]:  # عرض أول 5 أخطاء فقط
                        messages.warning(request, error)
                
                return redirect('/admin/import-excel/')
                
            except Exception as e:
                messages.error(request, f"خطأ في استيراد الملف: {str(e)}")
    else:
        form = ExcelImportForm()
    
    return render(request, 'admin/import_excel.html', {
        'form': form,
        'title': 'استيراد نتائج من Excel'
    })


@staff_member_required
def download_excel_template(request):
    """تحميل قالب Excel"""
    
    if request.method == 'POST':
        form = ExcelTemplateForm(request.POST)
        if form.is_valid():
            competition = form.cleaned_data['competition']
            
            # إنشاء ملف Excel
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "قالب النتائج"
            
            # إعداد الرؤوس
            headers = [
                'رقم_الطالب', 'الاسم_الأول', 'اسم_العائلة', 'الجنس',
                'الشعبة', 'المدرسة', 'الولاية', 'المعدل', 'الحالة'
            ]
            
            # كتابة الرؤوس
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # إضافة بيانات تجريبية
            sample_data = [
                ['10001', 'محمد', 'أحمد', 'M', 'العلوم الطبيعية', 'المواهب الجديدة', 'انواكشوط 1 (الشمالية)', '15.50', 'PASS'],
                ['10002', 'فاطمة', 'علي', 'F', 'الرياضيات', 'ثانوية الامتياز 1', 'انواكشوط 2 (الغربية)', '16.25', 'PASS'],
                ['10003', 'عبد الله', 'محمد', 'M', 'الآداب الأصلية', 'مدينة العلوم', 'انواكشوط 1 (الشمالية)', '14.75', 'PASS'],
            ]
            
            for row_num, row_data in enumerate(sample_data, 2):
                for col_num, value in enumerate(row_data, 1):
                    ws.cell(row=row_num, column=col_num, value=value)
            
            # تنسيق الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # إعداد الاستجابة
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="قالب_نتائج_{competition.name}.xlsx"'
            
            wb.save(response)
            return response
    else:
        form = ExcelTemplateForm()
    
    return render(request, 'admin/download_template.html', {
        'form': form,
        'title': 'تحميل قالب Excel'
    })


def update_rankings(competition):
    """تحديث ترتيب النتائج"""
    
    # الترتيب العام
    results = Result.objects.filter(competition=competition).order_by('-average')
    for rank, result in enumerate(results, 1):
        result.rank = rank
        result.save(update_fields=['rank'])
    
    # الترتيب في كل شعبة
    for subject in Subject.objects.all():
        subject_results = Result.objects.filter(
            competition=competition,
            subject=subject
        ).order_by('-average')
        
        for rank, result in enumerate(subject_results, 1):
            result.rank_in_subject = rank
            result.save(update_fields=['rank_in_subject'])


@staff_member_required
def delete_competition_view(request):
    """صفحة حذف المسابقات"""

    if request.method == 'POST':
        competition_id = request.POST.get('competition_id')
        if competition_id:
            try:
                competition = get_object_or_404(Competition, id=competition_id)
                competition_name = competition.name
                results_count = Result.objects.filter(competition=competition).count()

                # حذف جميع النتائج المرتبطة بالمسابقة
                Result.objects.filter(competition=competition).delete()

                # حذف المسابقة
                competition.delete()

                messages.success(
                    request,
                    f"تم حذف مسابقة '{competition_name}' و {results_count} نتيجة مرتبطة بها بنجاح"
                )

            except Exception as e:
                messages.error(request, f"خطأ في حذف المسابقة: {str(e)}")

        return redirect('/admin/delete-competition/')

    # عرض جميع المسابقات
    competitions = Competition.objects.all().order_by('-year', '-id')
    competitions_with_stats = []

    for competition in competitions:
        results_count = Result.objects.filter(competition=competition).count()
        subjects_count = Result.objects.filter(competition=competition).values('subject').distinct().count()

        competitions_with_stats.append({
            'competition': competition,
            'results_count': results_count,
            'subjects_count': subjects_count,
            'url': f"http://127.0.0.1:8000/{competition.slug}/"
        })

    return render(request, 'admin/delete_competition.html', {
        'competitions': competitions_with_stats,
        'title': 'حذف المسابقات'
    })


@staff_member_required
@require_POST
def delete_competition_confirm(request, competition_id):
    """تأكيد حذف المسابقة"""

    try:
        competition = get_object_or_404(Competition, id=competition_id)
        competition_name = competition.name
        results_count = Result.objects.filter(competition=competition).count()

        with transaction.atomic():
            # حذف جميع النتائج المرتبطة بالمسابقة
            Result.objects.filter(competition=competition).delete()

            # حذف المسابقة
            competition.delete()

        return JsonResponse({
            'success': True,
            'message': f"تم حذف مسابقة '{competition_name}' و {results_count} نتيجة مرتبطة بها بنجاح"
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f"خطأ في حذف المسابقة: {str(e)}"
        })
