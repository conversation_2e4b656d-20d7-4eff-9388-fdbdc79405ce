{% extends 'base.html' %}

{% block title %}{{ competition.name }} - موريباك{% endblock %}

{% block content %}
<!-- Competition Header -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-5 mb-3">{{ competition.name }}</h1>
        <p class="lead">أدخل رقم الطالب أو الطالبة للحصول على النتيجة</p>
    </div>
</section>

<!-- Search Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="search-box">
            <form method="GET" class="d-flex">
                <input type="text" 
                       name="search" 
                       class="form-control form-control-lg me-2" 
                       placeholder="أدخل رقم الطالب..."
                       value="{{ search_query }}"
                       required>
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
</section>

<!-- Search Result -->
{% if search_query %}
<section class="py-4">
    <div class="container">
        {% if search_result %}
        <div class="card result-card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    تم العثور على النتيجة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4>{{ search_result.student.full_name }}</h4>
                        <p class="mb-1"><strong>رقم الطالب:</strong> {{ search_result.student_number }}</p>
                        <p class="mb-1"><strong>الشعبة:</strong> {{ search_result.subject.name }}</p>
                        <p class="mb-1"><strong>المدرسة:</strong> {{ search_result.school.name }}</p>
                        <p class="mb-0"><strong>الولاية:</strong> {{ search_result.school.wilaya.name }}</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="rank-badge bg-primary text-white rounded">
                            المعدل: {{ search_result.average }}
                        </div>
                        {% if search_result.rank %}
                        <div class="mt-2">
                            <span class="badge bg-warning text-dark">الترتيب: {{ search_result.rank }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-warning text-center">
            <i class="fas fa-exclamation-triangle me-2"></i>
            لم يتم العثور على نتيجة للرقم: {{ search_query }}
        </div>
        {% endif %}
    </div>
</section>
{% endif %}

<!-- Subjects Statistics -->
<section class="py-5">
    <div class="container">
        <h3 class="text-center mb-4">إحصائيات الشعب</h3>
        <div class="row">
            {% for subject_stat in subjects_stats %}
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title">{{ subject_stat.subject__name }}</h6>
                        <p class="card-text">
                            <span class="badge bg-primary">{{ subject_stat.count }} طالب</span>
                        </p>
                        <a href="{% url 'subject_results' competition.slug subject_stat.subject__code %}" 
                           class="btn btn-sm btn-outline-primary">
                            عرض النتائج
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Top Students by Subject -->
{% if top_students_by_subject %}
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="text-center mb-5">الثلاثة الأوائل من كل شعبة</h3>

        {% for subject_name, subject_data in top_students_by_subject.items %}
        <div class="mb-5">
            <h4 class="mb-3">
                <a href="{% url 'subject_results' competition.slug subject_data.code %}"
                   class="text-decoration-none">
                    {{ subject_name }}
                </a>
            </h4>

            <div class="row">
                {% for result in subject_data.students %}
                <div class="col-md-4 mb-3">
                    <div class="card {% if forloop.counter == 1 %}top-student{% endif %}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-secondary">{{ forloop.counter }}</span>
                                {% if forloop.counter == 1 %}🥇{% elif forloop.counter == 2 %}🥈{% elif forloop.counter == 3 %}🥉{% endif %}
                            </div>

                            <h6 class="card-title">
                                <a href="{% url 'student_result' competition.slug result.student_number %}"
                                   class="text-decoration-none">
                                    {{ result.student.full_name }}
                                </a>
                            </h6>

                            <p class="card-text small mb-1">
                                <strong>المعدل:</strong> {{ result.average }}
                            </p>

                            <p class="card-text small mb-1">
                                <strong>المدرسة:</strong>
                                <a href="{% url 'school_results' competition.slug subject_data.code result.school.name|urlencode %}"
                                   class="text-decoration-none">
                                    {{ result.school.name }}
                                </a>
                            </p>

                            <p class="card-text small mb-0">
                                <strong>الولاية:</strong>
                                <a href="{% url 'wilaya_results' competition.slug subject_data.code result.school.wilaya.name|urlencode %}"
                                   class="text-decoration-none">
                                    {{ result.school.wilaya.name }}
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}
{% endblock %}
