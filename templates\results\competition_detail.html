{% extends 'base.html' %}

{% block title %}{{ competition.name }} - موريباك{% endblock %}

{% block content %}
<!-- Competition Header -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-5 mb-3">{{ competition.name }}</h1>
        <p class="lead">أدخل رقم الطالب أو الطالبة للحصول على النتيجة</p>
    </div>
</section>

<!-- Search Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="search-box search-container">
            <form method="GET" class="d-flex">
                <div class="input-group input-group-lg">
                    <input type="text"
                           name="search"
                           id="competitionSearchInput"
                           class="form-control"
                           placeholder="أدخل رقم الطالب أو الاسم..."
                           value="{{ search_query }}"
                           autocomplete="off">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </form>

            <!-- مؤشر البحث السريع -->
            <div id="quickSearchIndicator" class="quick-search-indicator" style="display: none;">
                <small class="text-muted">
                    <i class="fas fa-lightbulb me-1"></i>
                    اكتب للبحث السريع أو اضغط Enter للبحث الكامل
                </small>
            </div>
        </div>
    </div>
</section>

<!-- Search Result -->
{% if search_query %}
<section class="py-4">
    <div class="container">
        {% if search_result %}
        <div class="card result-card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    تم العثور على النتيجة
                </h5>
            </div>
            <div class="card-body">
                <!-- Student Info Card -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">معلومات الطالب</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>الرقم:</strong></td>
                                        <td>{{ search_result.student_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الاسم:</strong></td>
                                        <td>{{ search_result.student.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الجنس:</strong></td>
                                        <td>ذكر</td>
                                    </tr>
                                </table>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">معلومات المسابقة</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>المسابقة:</strong></td>
                                        <td>{{ competition.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الشعبة:</strong></td>
                                        <td>{{ search_result.subject.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المدرسة:</strong></td>
                                        <td>{{ search_result.school.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الولاية:</strong></td>
                                        <td>{{ search_result.school.wilaya.name }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Result Card -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h6 class="text-center mb-4" style="color: #667eea;">النتيجة</h6>

                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="card border-0" style="background: #f8f9fa;">
                                    <div class="card-body py-4">
                                        <h2 class="text-success mb-2">{{ search_result.rank|default:"2" }}</h2>
                                        <small class="text-muted">الترتيب في الشعبة</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 mb-3">
                                <div class="card border-0" style="background: #f8f9fa;">
                                    <div class="card-body py-4">
                                        <h2 class="text-primary mb-2">{{ search_result.average }}</h2>
                                        <small class="text-muted">المعدل</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 mb-3">
                                <div class="card border-0" style="background: #f8f9fa;">
                                    <div class="card-body py-4">
                                        <h2 class="text-warning mb-2">{{ search_result.rank|default:"2" }}</h2>
                                        <small class="text-muted">الترتيب العام</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 mb-3">
                                <div class="card border-0" style="background: #f8f9fa;">
                                    <div class="card-body py-4">
                                        <h2 class="mb-2" style="color: {% if search_result.status == 'PASS' %}#28a745{% elif search_result.status == 'SESSION' %}#ffc107{% elif search_result.status == 'FAIL' %}#dc3545{% else %}#6c757d{% endif %}">
                                            {% if search_result.status == 'PASS' %}ناجح
                                            {% elif search_result.status == 'SESSION' %}Session
                                            {% elif search_result.status == 'FAIL' %}راسب
                                            {% else %}غائب{% endif %}
                                        </h2>
                                        <small class="text-muted">نتيجة القرار</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <div class="row justify-content-center">
                        <div class="col-md-10">
                            <div class="d-flex flex-wrap justify-content-center gap-2">
                                <button onclick="window.print()" class="btn btn-success px-4 py-2">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة
                                </button>
                                <button onclick="location.reload()" class="btn btn-primary px-4 py-2">
                                    <i class="fas fa-search me-2"></i>
                                    بحث جديد
                                </button>
                                <a href="{% url 'student_result' competition.slug search_result.student_number %}" class="btn btn-primary px-4 py-2" target="_blank">
                                    <i class="fas fa-share-alt me-2"></i>
                                    عرض في صفحة منفصلة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-warning text-center">
            <i class="fas fa-exclamation-triangle me-2"></i>
            لم يتم العثور على نتيجة للرقم: {{ search_query }}
        </div>
        {% endif %}
    </div>
</section>
{% endif %}

<!-- Subjects Statistics -->
<section class="py-5">
    <div class="container">
        <h3 class="text-center mb-4">إحصائيات الشعب</h3>
        <div class="row">
            {% for subject_stat in subjects_stats %}
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title">{{ subject_stat.subject__name }}</h6>
                        <p class="card-text">
                            <span class="badge bg-primary">{{ subject_stat.count }} طالب</span>
                        </p>
                        <a href="{% url 'subject_results' competition.slug subject_stat.subject__code %}" 
                           class="btn btn-sm btn-outline-primary">
                            عرض النتائج
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Top Students by Subject -->
{% if top_students_by_subject %}
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="text-center mb-5">الثلاثة الأوائل من كل شعبة</h3>

        {% for subject_name, subject_data in top_students_by_subject.items %}
        <div class="mb-5">
            <h4 class="mb-3">
                <a href="{% url 'subject_results' competition.slug subject_data.code %}"
                   class="text-decoration-none">
                    {{ subject_name }}
                </a>
            </h4>

            <div class="row">
                {% for result in subject_data.students %}
                <div class="col-md-4 mb-3">
                    <div class="card {% if forloop.counter == 1 %}top-student{% endif %}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-secondary">{{ forloop.counter }}</span>
                                {% if forloop.counter == 1 %}🥇{% elif forloop.counter == 2 %}🥈{% elif forloop.counter == 3 %}🥉{% endif %}
                            </div>

                            <h6 class="card-title">
                                <a href="{% url 'student_result' competition.slug result.student_number %}"
                                   class="text-decoration-none">
                                    {{ result.student.full_name }}
                                </a>
                            </h6>

                            <p class="card-text small mb-1">
                                <strong>المعدل:</strong> {{ result.average }}
                            </p>

                            <p class="card-text small mb-1">
                                <strong>المدرسة:</strong>
                                <a href="{% url 'school_results' competition.slug subject_data.code result.school.name|urlencode %}"
                                   class="text-decoration-none">
                                    {{ result.school.name }}
                                </a>
                            </p>

                            <p class="card-text small mb-0">
                                <strong>الولاية:</strong>
                                <a href="{% url 'wilaya_results' competition.slug subject_data.code result.school.wilaya.name|urlencode %}"
                                   class="text-decoration-none">
                                    {{ result.school.wilaya.name }}
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}
{% endblock %}

{% block extra_js %}
<style>
/* Result Card Styles */
.result-card .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.result-card .card-body {
    padding: 1.5rem;
}

.result-card .table td {
    padding: 0.75rem 0.5rem;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
}

.result-card .table td:first-child {
    width: 35%;
    color: #6c757d;
    font-weight: 600;
}

.result-card .table td:last-child {
    color: #495057;
    font-weight: 500;
}

.result-card .btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.result-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.result-card .gap-2 {
    gap: 0.5rem !important;
}

.search-container {
    position: relative;
}

.quick-search-indicator {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0 0 8px 8px;
    padding: 8px 12px;
    z-index: 100;
}

.search-results-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

.search-results-container.loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.search-results-container.no-results {
    padding: 20px;
    text-align: center;
    color: #999;
}

.search-results-container.error {
    padding: 20px;
    text-align: center;
    color: #dc3545;
}

.search-results-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-results-header h6 {
    margin: 0;
    color: #333;
}

.results-count {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.search-result-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-main {
    margin-bottom: 8px;
}

.result-name {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.result-number {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.result-details {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.result-subject,
.result-average {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    color: #495057;
}

.result-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.result-status.status-success {
    background: #d4edda;
    color: #155724;
}

.result-status.status-danger {
    background: #f8d7da;
    color: #721c24;
}

.result-location {
    margin-top: 5px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('competitionSearchInput');
    const quickSearchIndicator = document.getElementById('quickSearchIndicator');

    if (searchInput) {
        // إظهار مؤشر البحث السريع عند التركيز
        searchInput.addEventListener('focus', function() {
            quickSearchIndicator.style.display = 'block';
            enableCompetitionLiveSearch(this);
        });

        // إخفاء المؤشر عند فقدان التركيز
        searchInput.addEventListener('blur', function() {
            setTimeout(() => {
                quickSearchIndicator.style.display = 'none';
                hideCompetitionSearchResults();
            }, 200);
        });

        // تفعيل البحث المباشر
        enableCompetitionLiveSearch(searchInput);
    }
});

// تفعيل البحث المباشر في صفحة المسابقة
function enableCompetitionLiveSearch(searchInput) {
    // إزالة المستمع السابق إن وجد
    searchInput.removeEventListener('input', handleCompetitionLiveSearch);

    // إضافة مستمع جديد
    searchInput.addEventListener('input', handleCompetitionLiveSearch);

    // إضافة مستمع للضغط على Enter
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            // إرسال النموذج للبحث الكامل
            this.form.submit();
        }
    });
}

// معالج البحث المباشر في المسابقة
let competitionSearchTimeout;
function handleCompetitionLiveSearch(e) {
    const query = e.target.value.trim();

    // إلغاء البحث السابق
    clearTimeout(competitionSearchTimeout);

    // إخفاء النتائج إذا كان النص فارغ
    if (query.length === 0) {
        hideCompetitionSearchResults();
        return;
    }

    // البحث بعد توقف الكتابة لمدة 300ms
    competitionSearchTimeout = setTimeout(() => {
        if (query.length >= 2) {
            performCompetitionLiveSearch(query);
        }
    }, 300);
}

// تنفيذ البحث المباشر في المسابقة
function performCompetitionLiveSearch(query) {
    showCompetitionSearchResults('🔍 جاري البحث...', 'loading');

    // الحصول على slug المسابقة من URL
    const competitionSlug = getCompetitionSlugFromUrl();

    fetch(`/api/search/?q=${encodeURIComponent(query)}&competition=${competitionSlug}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.results.length > 0) {
                displayCompetitionSearchResults(data.results, query);
            } else {
                showCompetitionSearchResults(`❌ لم يتم العثور على نتائج للبحث: "${query}"`, 'no-results');
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            showCompetitionSearchResults('⚠️ خطأ في البحث، يرجى المحاولة مرة أخرى', 'error');
        });
}

// عرض نتائج البحث في المسابقة
function displayCompetitionSearchResults(results, query) {
    let html = `
        <div class="search-results-header">
            <h6>🔍 نتائج البحث عن: "${query}"</h6>
            <span class="results-count">${results.length} نتيجة</span>
        </div>
    `;

    results.forEach(result => {
        const statusIcon = result.status === 'PASS' ? '✅' : '❌';
        const statusText = result.status === 'PASS' ? 'ناجح' : 'راسب';
        const statusClass = result.status === 'PASS' ? 'success' : 'danger';

        html += `
            <div class="search-result-item" onclick="selectSearchResult('${result.student_number}')">
                <div class="result-main">
                    <h6 class="result-name">👤 ${result.student_name}</h6>
                    <p class="result-number">📋 رقم الطالب: ${result.student_number}</p>
                </div>
                <div class="result-details">
                    <span class="result-subject">📚 ${result.subject}</span>
                    <span class="result-average">📊 ${result.average}</span>
                    <span class="result-status status-${statusClass}">${statusIcon} ${statusText}</span>
                </div>
                <div class="result-location">
                    <small class="text-muted">🏫 ${result.school} • 📍 ${result.wilaya}</small>
                </div>
            </div>
        `;
    });

    showCompetitionSearchResults(html, 'results');
}

// إظهار نتائج البحث في المسابقة
function showCompetitionSearchResults(content, type) {
    let resultsContainer = document.getElementById('competitionSearchResults');

    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'competitionSearchResults';
        resultsContainer.className = 'search-results-container';

        const searchContainer = document.querySelector('.search-container');
        searchContainer.appendChild(resultsContainer);
    }

    resultsContainer.className = `search-results-container ${type}`;
    resultsContainer.innerHTML = content;
    resultsContainer.style.display = 'block';
}

// إخفاء نتائج البحث في المسابقة
function hideCompetitionSearchResults() {
    const resultsContainer = document.getElementById('competitionSearchResults');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

// اختيار نتيجة البحث
function selectSearchResult(studentNumber) {
    const searchInput = document.getElementById('competitionSearchInput');
    searchInput.value = studentNumber;
    hideCompetitionSearchResults();

    // إرسال النموذج للبحث الكامل
    searchInput.form.submit();
}

// الحصول على slug المسابقة من URL
function getCompetitionSlugFromUrl() {
    const path = window.location.pathname;
    const segments = path.split('/').filter(segment => segment.length > 0);
    return segments[0] || '';
}

// إخفاء النتائج عند النقر خارجها
document.addEventListener('click', function(e) {
    const searchContainer = document.querySelector('.search-container');
    if (searchContainer && !searchContainer.contains(e.target)) {
        hideCompetitionSearchResults();
    }
});
</script>
{% endblock %}
