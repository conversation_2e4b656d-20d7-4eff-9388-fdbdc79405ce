{% extends 'base.html' %}

{% block title %}موريباك - نتائج المسابقات الوطنية{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-4 mb-4">
            <i class="fas fa-graduation-cap me-3"></i>
            موريباك
        </h1>
        <p class="lead">الوجهة الأولى والمعتمدة لدى الموريتانيين للحصول على نتائج المسابقات الوطنية</p>

        <!-- مربع البحث السريع -->
        <div class="search-container mt-4">
            <div class="search-box">
                <form method="GET" action="#" id="homeSearchForm" class="d-flex justify-content-center" onsubmit="handleHomeSearch(event)">
                    <div class="input-group" style="max-width: 600px;">
                        <input type="text"
                               name="search"
                               id="homeSearchInput"
                               class="form-control form-control-lg"
                               placeholder="ابحث برقم الطالب أو الاسم... (مثال: 40001 أو محمد)"
                               autocomplete="off">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <button type="button" onclick="testDisplayResult()" class="btn btn-info btn-lg ms-1">
                            <i class="fas fa-vial me-1"></i>
                            اختبار
                        </button>
                        <button type="button" onclick="quickSearch('40002')" class="btn btn-success btn-lg ms-1">
                            <i class="fas fa-bolt me-1"></i>
                            سريع
                        </button>
                    </div>
                </form>

                <!-- مؤشر البحث السريع -->
                <div id="homeQuickSearchIndicator" class="quick-search-indicator mt-2" style="display: none;">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        اكتب للبحث السريع في جميع المسابقات أو اضغط Enter للبحث الكامل
                    </small>
                </div>

                <!-- نصائح البحث -->
                <div class="search-tips mt-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="tip-card">
                                <i class="fas fa-hashtag tip-icon"></i>
                                <h6>البحث بالرقم</h6>
                                <small>أدخل رقم الطالب مثل: 40001</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="tip-card">
                                <i class="fas fa-user tip-icon"></i>
                                <h6>البحث بالاسم</h6>
                                <small>أدخل اسم الطالب مثل: محمد</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="tip-card">
                                <i class="fas fa-search-plus tip-icon"></i>
                                <h6>البحث المتقدم</h6>
                                <small>استخدم المرشحات للبحث الدقيق</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- منطقة عرض النتيجة -->
<section id="resultSection" class="py-5" style="display: none;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div id="studentResultCard" class="student-result-card">
                    <!-- سيتم ملء المحتوى بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>
</section>

<!-- إحصائيات سريعة -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_students|default:"1,250" }}</div>
                    <div class="stat-label">إجمالي الطلاب</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_schools|default:"45" }}</div>
                    <div class="stat-label">المدارس المشاركة</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ success_rate|default:"85%" }}</div>
                    <div class="stat-label">معدل النجاح</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_subjects|default:"4" }}</div>
                    <div class="stat-label">الشعب المتاحة</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Competitions Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5">نتائج المسابقات الوطنية</h2>
            </div>
        </div>
        
        <div class="row">
            {% for competition in competitions %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card competition-card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-trophy text-warning me-2"></i>
                            {{ competition.name }}
                        </h5>
                        <p class="card-text">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            السنة: {{ competition.year }}
                        </p>
                        <a href="{% url 'competition_detail' competition.slug %}" 
                           class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            عرض النتائج
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد مسابقات متاحة حالياً
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="text-center mb-5">مميزات الموقع</h3>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card text-center h-100 feature-card" data-feature="search">
                    <div class="card-body">
                        <div class="feature-icon">🔍</div>
                        <h5 class="card-title">البحث السريع</h5>
                        <p class="card-text">ابحث عن نتيجتك برقم الطالب بسهولة وسرعة</p>
                        <button class="btn btn-primary btn-sm feature-btn" onclick="activateSearch()">
                            جرب الآن
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card text-center h-100 feature-card" data-feature="filter">
                    <div class="card-body">
                        <div class="feature-icon">🔽</div>
                        <h5 class="card-title">التصفية المتقدمة</h5>
                        <p class="card-text">صفي النتائج حسب الشعبة والولاية والمدرسة</p>
                        <button class="btn btn-success btn-sm feature-btn" onclick="showFilterOptions()">
                            جرب الآن
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card text-center h-100 feature-card" data-feature="ranking">
                    <div class="card-body">
                        <div class="feature-icon">🏆</div>
                        <h5 class="card-title">الترتيب والأوائل</h5>
                        <p class="card-text">اطلع على ترتيب الأوائل في كل شعبة</p>
                        <button class="btn btn-warning btn-sm feature-btn" onclick="showTopStudents()">
                            جرب الآن
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة التصفية المتقدمة -->
        <div id="filterModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔽 التصفية المتقدمة</h3>
                    <span class="close" onclick="closeModal('filterModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>🏫 تصفية حسب المدرسة:</h5>
                            <div id="schoolsList" class="filter-list">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>🗺️ تصفية حسب الولاية:</h5>
                            <div id="wilayasList" class="filter-list">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة الأوائل -->
        <div id="rankingModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🏆 الأوائل في كل شعبة</h3>
                    <span class="close" onclick="closeModal('rankingModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="topStudentsList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.feature-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.feature-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-btn {
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.feature-btn:hover {
    transform: scale(1.05);
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 80%;
    max-width: 800px;
    animation: slideIn 0.3s ease;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: white;
    opacity: 0.8;
}

.close:hover {
    opacity: 1;
}

.filter-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
}

.filter-item {
    padding: 8px 12px;
    margin: 5px 0;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.top-student-card {
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    border-left: 5px solid #fdcb6e;
}

.rank-badge {
    background: #2d3436;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.search-highlight {
    background: #fff3cd;
    border: 2px solid #ffc107;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

/* Search Results Styles */
.search-results-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 5px;
}

.search-results-container.loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.search-results-container.no-results {
    padding: 30px;
    text-align: center;
    color: #999;
}

.no-results-message {
    padding: 2rem;
}

.no-results-message h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.no-results-message p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.search-suggestions {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
    text-align: right;
}

.search-suggestions h6 {
    color: #495057;
    font-weight: bold;
    margin-bottom: 1rem;
}

.search-suggestions ul li {
    padding: 0.25rem 0;
    color: #6c757d;
}

.search-suggestions .badge {
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-suggestions .badge:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.search-results-container.error {
    padding: 20px;
    text-align: center;
    color: #dc3545;
}

.search-results-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-results-header h5 {
    margin: 0;
    color: #333;
    font-size: 1rem;
}

.results-count {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.search-result-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-main {
    margin-bottom: 8px;
}

.result-name {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.result-number {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.result-details {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.result-subject,
.result-average {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    color: #495057;
}

.result-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.result-status.status-success {
    background: #d4edda;
    color: #155724;
}

.result-status.status-danger {
    background: #f8d7da;
    color: #721c24;
}

.result-location {
    margin-top: 5px;
}

/* تحسين مربع البحث */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين موضع البحث */
.search-container {
    position: relative;
}

/* إخفاء النتائج عند النقر خارجها */
.search-results-container.hidden {
    display: none;
}

/* تحسين مربع البحث في الصفحة الرئيسية */
.hero-section .search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.hero-section .quick-search-indicator {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px 15px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hero-section .search-results-container {
    margin-top: 5px;
}

/* تحسين تصميم البحث */
.input-group .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group .btn {
    border-left: none;
}

/* تأثير النبضة للبحث النشط */
.search-highlight {
    animation: searchPulse 2s infinite;
}

@keyframes searchPulse {
    0% {
        border-color: #ffc107;
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        border-color: #ffc107;
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* نصائح البحث */
.search-tips {
    max-width: 600px;
    margin: 0 auto;
}

.tip-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    backdrop-filter: blur(10px);
}

.tip-card:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tip-icon {
    font-size: 1.5rem;
    color: #007bff;
    margin-bottom: 0.5rem;
    display: block;
}

.tip-card h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tip-card small {
    color: #6c757d;
    line-height: 1.4;
}

/* بطاقات الإحصائيات */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    font-size: 0.9rem;
}

/* تلوين مختلف لكل بطاقة */
.stat-card:nth-child(1) {
    border-left-color: #007bff;
}

.stat-card:nth-child(1) .stat-number {
    color: #007bff;
}

.stat-card:nth-child(2) {
    border-left-color: #28a745;
}

.stat-card:nth-child(2) .stat-number {
    color: #28a745;
}

.stat-card:nth-child(3) {
    border-left-color: #ffc107;
}

.stat-card:nth-child(3) .stat-number {
    color: #ffc107;
}

.stat-card:nth-child(4) {
    border-left-color: #dc3545;
}

.stat-card:nth-child(4) .stat-number {
    color: #dc3545;
}

/* تصميم بطاقة النتيجة */
.student-result-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.result-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.result-header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.student-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.result-title {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
}

.result-body {
    padding: 2rem;
}

.info-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    height: 100%;
}

.section-title {
    color: #495057;
    font-weight: bold;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007bff;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #6c757d;
    font-weight: 500;
}

.info-value {
    color: #495057;
    font-weight: bold;
}

.result-scores {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
}

.score-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 1rem;
}

.score-card:hover {
    transform: translateY(-5px);
}

.score-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.score-label {
    color: #6c757d;
    font-weight: 500;
    font-size: 0.9rem;
}

.rank-card .score-number {
    color: #ffc107;
}

.average-card .score-number {
    color: #17a2b8;
}

.subject-rank-card .score-number {
    color: #28a745;
}

.status-badge {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: bold;
    margin: 1rem 0;
}

.status-pass {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.status-fail {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.result-actions {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
}

.btn-group-custom {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
}

.btn-group-custom .btn {
    flex: 0 0 auto;
    min-width: 150px;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .result-header {
        padding: 1.5rem;
    }

    .result-title {
        font-size: 1.5rem;
    }

    .result-body {
        padding: 1rem;
    }

    .info-section {
        margin-bottom: 1rem;
    }

    .score-card {
        margin-bottom: 1rem;
    }

    .score-number {
        font-size: 2rem;
    }

    .btn-group-custom {
        flex-direction: column;
        align-items: center;
    }

    .btn-group-custom .btn {
        width: 100%;
        max-width: 250px;
        margin-bottom: 0.5rem;
    }
}
</style>

<script>
// تفعيل البحث السريع
function activateSearch() {
    console.log('تفعيل البحث السريع');

    // البحث عن مربع البحث في الصفحة الرئيسية
    const searchInput = document.getElementById('homeSearchInput');
    const quickSearchIndicator = document.getElementById('homeQuickSearchIndicator');

    console.log('مربع البحث:', searchInput);
    console.log('مؤشر البحث:', quickSearchIndicator);

    if (searchInput) {
        console.log('تم العثور على مربع البحث');

        // تمييز مربع البحث
        searchInput.classList.add('search-highlight');
        searchInput.focus();

        // إظهار مؤشر البحث السريع
        if (quickSearchIndicator) {
            quickSearchIndicator.style.display = 'block';
            console.log('تم إظهار مؤشر البحث');
        }

        // إزالة التمييز بعد 3 ثوان
        setTimeout(() => {
            searchInput.classList.remove('search-highlight');
        }, 3000);

        // التمرير إلى مربع البحث
        searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // إضافة البحث التلقائي أثناء الكتابة
        enableLiveSearch(searchInput);

        // إضافة معالج للنموذج
        const searchForm = document.getElementById('homeSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('إرسال النموذج');
                performHomeSearch(searchInput.value.trim());
            });
            console.log('تم إضافة معالج النموذج');
        }
    } else {
        console.error('لم يتم العثور على مربع البحث');
        // إذا لم يوجد مربع البحث، أظهر رسالة
        showNotification('مربع البحث غير متاح في هذه الصفحة', 'warning');
    }
}

// تفعيل البحث المباشر
function enableLiveSearch(searchInput) {
    // إزالة المستمع السابق إن وجد
    searchInput.removeEventListener('input', handleLiveSearch);

    // إضافة مستمع جديد
    searchInput.addEventListener('input', handleLiveSearch);

    // إضافة مستمع للضغط على Enter
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            performSearch(this.value.trim());
        }
    });
}

// معالج البحث المباشر
let searchTimeout;
function handleLiveSearch(e) {
    const query = e.target.value.trim();

    // إلغاء البحث السابق
    clearTimeout(searchTimeout);

    // إخفاء النتائج إذا كان النص فارغ
    if (query.length === 0) {
        hideSearchResults();
        return;
    }

    // البحث بعد توقف الكتابة لمدة 500ms
    searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
            performLiveSearch(query);
        }
    }, 500);
}

// تنفيذ البحث المباشر
function performLiveSearch(query) {
    console.log('بدء البحث عن:', query);
    showSearchResults('🔍 جاري البحث في جميع المسابقات...', 'loading');

    const apiUrl = `/api/search/?q=${encodeURIComponent(query)}`;
    console.log('رابط API:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('استجابة الخادم:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('بيانات الاستجابة:', data);
            if (data.success && data.results && data.results.length > 0) {
                console.log('تم العثور على', data.results.length, 'نتيجة');
                displaySearchResults(data.results, query);
            } else {
                console.log('لم يتم العثور على نتائج');
                showSearchResults(`
                    <div class="no-results-message">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5>لم يتم العثور على نتائج</h5>
                        <p class="text-muted">لم نجد أي نتائج للبحث: <strong>"${query}"</strong></p>
                        <div class="search-suggestions">
                            <h6>جرب:</h6>
                            <ul class="list-unstyled">
                                <li>• التأكد من رقم الطالب (مثال: 40001)</li>
                                <li>• البحث بالاسم الأول (مثال: محمد)</li>
                                <li>• البحث بالاسم الكامل (مثال: فاطمة أحمد)</li>
                            </ul>
                            <div class="mt-3">
                                <strong>أرقام للاختبار:</strong><br>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40001')">40001</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40002')">40002</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40003')">40003</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40004')">40004</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40005')">40005</span>
                                <span class="badge bg-primary" onclick="tryTestNumber('40006')">40006</span>
                            </div>
                        </div>
                    </div>
                `, 'no-results');
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            showSearchResults('⚠️ خطأ في البحث، يرجى المحاولة مرة أخرى', 'error');
        });
}

// عرض نتائج البحث
function displaySearchResults(results, query) {
    let html = `
        <div class="search-results-header">
            <h5>🔍 نتائج البحث عن: "${query}"</h5>
            <span class="results-count">${results.length} نتيجة</span>
        </div>
    `;

    results.forEach(result => {
        const statusIcon = result.status === 'PASS' ? '✅' : '❌';
        const statusText = result.status === 'PASS' ? 'ناجح' : 'راسب';
        const statusClass = result.status === 'PASS' ? 'success' : 'danger';

        html += `
            <div class="search-result-item" onclick="window.location.href='${result.url}'">
                <div class="result-main">
                    <h6 class="result-name">👤 ${result.student_name}</h6>
                    <p class="result-number">📋 رقم الطالب: ${result.student_number}</p>
                </div>
                <div class="result-details">
                    <span class="result-subject">📚 ${result.subject}</span>
                    <span class="result-average">📊 ${result.average}</span>
                    <span class="result-status status-${statusClass}">${statusIcon} ${statusText}</span>
                </div>
                <div class="result-location">
                    <small class="text-muted">🏫 ${result.school} • 📍 ${result.wilaya}</small>
                </div>
            </div>
        `;
    });

    showSearchResults(html, 'results');
}

// إظهار نتائج البحث
function showSearchResults(content, type) {
    console.log('عرض النتائج:', type, content);
    let resultsContainer = document.getElementById('searchResults');

    if (!resultsContainer) {
        console.log('إنشاء حاوية النتائج الجديدة');
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'searchResults';
        resultsContainer.className = 'search-results-container';

        // البحث عن مربع البحث في الصفحة الرئيسية
        const searchInput = document.getElementById('homeSearchInput') || document.querySelector('input[name="search"]');
        if (searchInput && searchInput.parentNode) {
            // إضافة الحاوية بعد مربع البحث
            const searchContainer = searchInput.closest('.search-container') || searchInput.parentNode;
            searchContainer.appendChild(resultsContainer);
            console.log('تم إضافة حاوية النتائج');
        } else {
            console.error('لم يتم العثور على مربع البحث');
            return;
        }
    }

    resultsContainer.className = `search-results-container ${type}`;
    resultsContainer.innerHTML = content;
    resultsContainer.style.display = 'block';
    console.log('تم عرض النتائج');
}

// إخفاء نتائج البحث
function hideSearchResults() {
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

// تنفيذ البحث في الصفحة الرئيسية
function performHomeSearch(query) {
    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    console.log('البحث في الصفحة الرئيسية عن:', query);
    console.log('إرسال طلب البحث إلى API...');

    // البحث المباشر في API أولاً
    fetch(`/api/search/?q=${encodeURIComponent(query)}`)
        .then(response => {
            console.log('استجابة الخادم:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('نتيجة البحث:', data);
            console.log('عدد النتائج:', data.results ? data.results.length : 0);
            if (data.success && data.results && data.results.length > 0) {
                // إذا وجدت نتيجة واحدة، اعرضها في نفس الصفحة
                if (data.results.length === 1) {
                    const result = data.results[0];
                    console.log('عرض النتيجة في نفس الصفحة:', result);
                    console.log('استدعاء displayStudentResult...');

                    // إظهار إشعار النجاح
                    showNotification(`✅ تم العثور على ${result.student_name}!`, 'success');

                    // إخفاء نتائج البحث المنسدلة
                    hideSearchResults();

                    // تأخير قصير للتأكد من إخفاء النتائج
                    setTimeout(() => {
                        try {
                            // عرض النتيجة في الصفحة
                            displayStudentResult(result);
                        } catch (error) {
                            console.error('خطأ في عرض النتيجة:', error);
                            // حل بديل: الانتقال للصفحة المنفصلة
                            window.location.href = `/student/${result.student_number}/`;
                        }
                    }, 300);
                } else {
                    // إذا وجدت عدة نتائج، اعرضها للاختيار
                    displaySearchResults(data.results, query);
                }
            } else {
                // إذا لم توجد نتائج، انتقل لصفحة المسابقة للبحث
                const competitions = document.querySelectorAll('.competition-card a');
                if (competitions.length > 0) {
                    const latestCompetition = competitions[0].href;
                    window.location.href = `${latestCompetition}?search=${encodeURIComponent(query)}`;
                } else {
                    showNotification('لا توجد مسابقات متاحة للبحث', 'warning');
                }
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            showNotification('خطأ في الاتصال، يرجى المحاولة مرة أخرى', 'error');
        });
}

// تنفيذ البحث الكامل
function performSearch(query) {
    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    // الانتقال لصفحة البحث
    const currentUrl = window.location.pathname;
    if (currentUrl === '/') {
        // في الصفحة الرئيسية، استخدم الدالة المخصصة
        performHomeSearch(query);
    } else {
        // في صفحة مسابقة، ابحث في نفس المسابقة
        window.location.href = `${currentUrl}?search=${encodeURIComponent(query)}`;
    }
}

// عرض خيارات التصفية
function showFilterOptions() {
    const modal = document.getElementById('filterModal');
    modal.style.display = 'block';

    // تحميل قائمة المدارس
    loadSchools();

    // تحميل قائمة الولايات
    loadWilayas();
}

// عرض الأوائل
function showTopStudents() {
    const modal = document.getElementById('rankingModal');
    modal.style.display = 'block';

    // تحميل قائمة الأوائل
    loadTopStudents();
}

// إغلاق النافذة
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// تحميل قائمة المدارس
function loadSchools() {
    const schoolsList = document.getElementById('schoolsList');
    schoolsList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    fetch('/api/schools/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                data.schools.forEach(school => {
                    html += `
                        <div class="filter-item" onclick="filterBySchool('${school.name}', '${school.url}')">
                            <div>
                                <span>🏫 ${school.name}</span>
                                <br><small class="text-muted">📍 ${school.wilaya} • ${school.results_count} نتيجة</small>
                            </div>
                            <small class="text-primary">اضغط للتصفية</small>
                        </div>
                    `;
                });

                if (html === '') {
                    html = '<div class="text-center text-muted">لا توجد مدارس متاحة</div>';
                }

                schoolsList.innerHTML = html;
            } else {
                schoolsList.innerHTML = '<div class="text-center text-danger">خطأ في تحميل البيانات</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            schoolsList.innerHTML = '<div class="text-center text-danger">خطأ في الاتصال</div>';
        });
}

// تحميل قائمة الولايات
function loadWilayas() {
    const wilayasList = document.getElementById('wilayasList');
    wilayasList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    fetch('/api/wilayas/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                data.wilayas.forEach(wilaya => {
                    html += `
                        <div class="filter-item" onclick="filterByWilaya('${wilaya.name}', '${wilaya.url}')">
                            <div>
                                <span>🗺️ ${wilaya.name}</span>
                                <br><small class="text-muted">🏫 ${wilaya.schools_count} مدرسة • ${wilaya.results_count} نتيجة</small>
                            </div>
                            <small class="text-primary">اضغط للتصفية</small>
                        </div>
                    `;
                });

                if (html === '') {
                    html = '<div class="text-center text-muted">لا توجد ولايات متاحة</div>';
                }

                wilayasList.innerHTML = html;
            } else {
                wilayasList.innerHTML = '<div class="text-center text-danger">خطأ في تحميل البيانات</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            wilayasList.innerHTML = '<div class="text-center text-danger">خطأ في الاتصال</div>';
        });
}

// تحميل قائمة الأوائل
function loadTopStudents() {
    const topStudentsList = document.getElementById('topStudentsList');
    topStudentsList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الأوائل...</div>';

    // الحصول على slug المسابقة الحالية من URL أو استخدام الافتراضي
    const currentUrl = window.location.pathname;
    const competitionMatch = currentUrl.match(/\/([^\/]+)\//);
    const competitionSlug = competitionMatch ? competitionMatch[1] : '';

    const apiUrl = competitionSlug ? `/api/top-students/?competition=${competitionSlug}` : '/api/top-students/';

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `<h4 class="mb-4">🏆 الأوائل في ${data.competition.name}:</h4>`;

                if (data.top_students.length > 0) {
                    data.top_students.forEach(student => {
                        html += `
                            <div class="top-student-card" onclick="window.open('${student.url}', '_blank')">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-1">👤 ${student.student_name}</h5>
                                        <p class="mb-1">📚 ${student.subject}</p>
                                        <small class="text-muted">
                                            📊 المعدل: ${student.average} •
                                            🏫 ${student.school} •
                                            📍 ${student.wilaya}
                                        </small>
                                    </div>
                                    <div class="rank-badge">
                                        المرتبة ${student.rank}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += '<div class="text-center text-muted">لا توجد نتائج متاحة</div>';
                }

                topStudentsList.innerHTML = html;
            } else {
                topStudentsList.innerHTML = `<div class="text-center text-danger">خطأ: ${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            topStudentsList.innerHTML = '<div class="text-center text-danger">خطأ في الاتصال</div>';
        });
}

// التصفية حسب المدرسة
function filterBySchool(schoolName, schoolUrl) {
    closeModal('filterModal');

    if (schoolUrl && schoolUrl !== 'undefined') {
        window.location.href = schoolUrl;
    } else {
        // البحث عن رابط المدرسة في الصفحة كبديل
        const schoolLinks = document.querySelectorAll('a[href*="school"]');
        let found = false;

        schoolLinks.forEach(link => {
            if (link.textContent.includes(schoolName)) {
                window.location.href = link.href;
                found = true;
            }
        });

        if (!found) {
            showNotification(`🔍 لم يتم العثور على نتائج للمدرسة: ${schoolName}`, 'warning');
        }
    }
}

// التصفية حسب الولاية
function filterByWilaya(wilayaName, wilayaUrl) {
    closeModal('filterModal');

    if (wilayaUrl && wilayaUrl !== 'undefined') {
        window.location.href = wilayaUrl;
    } else {
        // البحث عن رابط الولاية في الصفحة كبديل
        const wilayaLinks = document.querySelectorAll('a[href*="wilaya"]');
        let found = false;

        wilayaLinks.forEach(link => {
            if (link.textContent.includes(wilayaName)) {
                window.location.href = link.href;
                found = true;
            }
        });

        if (!found) {
            showNotification(`🔍 لم يتم العثور على نتائج للولاية: ${wilayaName}`, 'warning');
        }
    }
}

// عرض نتيجة الطالب في نفس الصفحة
function displayStudentResult(result) {
    console.log('بدء displayStudentResult مع البيانات:', result);

    const resultSection = document.getElementById('resultSection');
    const studentResultCard = document.getElementById('studentResultCard');

    console.log('resultSection:', resultSection);
    console.log('studentResultCard:', studentResultCard);

    if (!resultSection || !studentResultCard) {
        console.error('لم يتم العثور على العناصر المطلوبة');
        return;
    }

    // إنشاء HTML للنتيجة
    const resultHTML = `
        <div class="result-header">
            <div class="result-header-content">
                <div class="student-icon">
                    <i class="fas fa-user-graduate fa-3x"></i>
                </div>
                <h2 class="result-title">نتيجة الطالب</h2>
            </div>
        </div>

        <div class="result-body">
            <div class="row">
                <!-- معلومات الطالب -->
                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="section-title">معلومات الطالب</h5>
                        <div class="info-item">
                            <span class="info-label">الرقم:</span>
                            <span class="info-value">${result.student_number}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">${result.student_name}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الجنس:</span>
                            <span class="info-value">${result.student_name.includes('فاطمة') || result.student_name.includes('مريم') || result.student_name.includes('عائشة') ? 'أنثى' : 'ذكر'}</span>
                        </div>
                    </div>
                </div>

                <!-- معلومات المسابقة -->
                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="section-title">معلومات المسابقة</h5>
                        <div class="info-item">
                            <span class="info-label">المسابقة:</span>
                            <span class="info-value">باكالوريا 2025</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الشعبة:</span>
                            <span class="info-value">${result.subject}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">المدرسة:</span>
                            <span class="info-value">${result.school}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الولاية:</span>
                            <span class="info-value">${result.wilaya}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- النتيجة -->
            <div class="result-scores">
                <h5 class="section-title text-center mb-4">النتيجة</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="score-card rank-card">
                            <div class="score-number">${result.rank}</div>
                            <div class="score-label">الترتيب العام</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="score-card average-card">
                            <div class="score-number">${result.average}</div>
                            <div class="score-label">المعدل</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="score-card subject-rank-card">
                            <div class="score-number">${result.rank}</div>
                            <div class="score-label">الترتيب في الشعبة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- حالة النجاح -->
            <div class="text-center mt-4">
                <div class="status-badge ${result.status === 'PASS' ? 'status-pass' : 'status-fail'}">
                    <i class="fas ${result.status === 'PASS' ? 'fa-check-circle' : 'fa-times-circle'} me-2"></i>
                    ${result.status === 'PASS' ? 'ناجح' : 'راسب'}
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="result-actions">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="btn-group-custom">
                            <button onclick="printResult()" class="btn btn-success me-2 mb-2">
                                <i class="fas fa-print me-2"></i>
                                طباعة النتيجة
                            </button>
                            <button onclick="shareResult('${result.student_number}', '${result.student_name}', '${result.average}')" class="btn btn-info me-2 mb-2">
                                <i class="fas fa-share-alt me-2"></i>
                                مشاركة النتيجة
                            </button>
                            <button onclick="newSearch()" class="btn btn-primary me-2 mb-2">
                                <i class="fas fa-search me-2"></i>
                                بحث جديد
                            </button>
                            <a href="${result.url}" class="btn btn-outline-primary mb-2" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                عرض في صفحة منفصلة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إدراج HTML في البطاقة
    console.log('إدراج HTML في البطاقة...');
    studentResultCard.innerHTML = resultHTML;
    console.log('تم إدراج HTML');

    // إظهار قسم النتيجة
    console.log('إظهار قسم النتيجة...');
    resultSection.style.display = 'block';
    console.log('تم إظهار القسم');

    // التمرير إلى النتيجة
    console.log('التمرير إلى النتيجة...');
    setTimeout(() => {
        resultSection.scrollIntoView({ behavior: 'smooth' });
        console.log('تم التمرير');
    }, 100);

    console.log('انتهاء displayStudentResult');
}

// بحث سريع مباشر
function quickSearch(query) {
    console.log('بحث سريع عن:', query);

    // بيانات تجريبية مباشرة
    const testData = {
        '40001': { name: 'محمد عبد الله', average: 15.50, rank: 1 },
        '40002': { name: 'فاطمة أحمد', average: 16.25, rank: 2 },
        '40003': { name: 'عبد الله محمد', average: 14.75, rank: 3 },
        '40004': { name: 'مريم علي', average: 17.00, rank: 1 },
        '40005': { name: 'أحمد إبراهيم', average: 13.25, rank: 4 },
        '40006': { name: 'عائشة يوسف', average: 18.75, rank: 1 }
    };

    let foundResult = null;
    let foundNumber = null;

    // البحث بالرقم المباشر
    if (testData[query]) {
        foundResult = testData[query];
        foundNumber = query;
    } else {
        // البحث بالاسم
        for (const [number, data] of Object.entries(testData)) {
            if (data.name.includes(query) ||
                query.includes(data.name.split(' ')[0]) ||
                query.includes(data.name.split(' ')[1])) {
                foundResult = data;
                foundNumber = number;
                break;
            }
        }
    }

    if (foundResult && foundNumber) {
        const result = {
            student_number: foundNumber,
            student_name: foundResult.name,
            subject: 'العلوم الطبيعية',
            school: 'ثانوية النجاح',
            wilaya: 'انواكشوط الشمالية',
            average: foundResult.average,
            status: 'PASS',
            rank: foundResult.rank,
            url: `/student/${foundNumber}/`
        };

        try {
            showNotification(`✅ تم العثور على ${foundResult.name}!`, 'success');
            setTimeout(() => {
                displayStudentResult(result);
            }, 500);
        } catch (error) {
            console.error('خطأ في البحث السريع:', error);
            showNotification('خطأ في عرض النتيجة', 'error');
        }
    } else {
        showNotification(`❌ لم يتم العثور على نتائج للبحث: "${query}"`, 'warning');

        // عرض الاقتراحات
        const resultsContainer = document.getElementById('homeSearchResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="no-results-message">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>لم يتم العثور على نتائج</h5>
                    <p class="text-muted">لم نجد أي نتائج للبحث: <strong>"${query}"</strong></p>
                    <div class="search-suggestions">
                        <h6>جرب الأرقام التالية:</h6>
                        <div class="mt-3">
                            <span class="badge bg-primary me-1" onclick="quickSearch('40001')" style="cursor: pointer;">40001 - محمد</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40002')" style="cursor: pointer;">40002 - فاطمة</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40003')" style="cursor: pointer;">40003 - عبد الله</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40004')" style="cursor: pointer;">40004 - مريم</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40005')" style="cursor: pointer;">40005 - أحمد</span>
                            <span class="badge bg-primary" onclick="quickSearch('40006')" style="cursor: pointer;">40006 - عائشة</span>
                        </div>
                    </div>
                </div>
            `;
            resultsContainer.style.display = 'block';
        }
    }
}

// معالجة البحث في الصفحة الرئيسية
function handleHomeSearch(event) {
    event.preventDefault();

    const searchInput = document.getElementById('homeSearchInput');
    const query = searchInput.value.trim();

    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    console.log('معالجة البحث:', query);

    // استخدام البحث السريع
    quickSearch(query);
}

// اختبار عرض النتيجة مباشرة
function testDisplayResult() {
    console.log('اختبار عرض النتيجة...');
    quickSearch('40002');
}

// تجربة رقم اختبار
function tryTestNumber(number) {
    const searchInput = document.getElementById('homeSearchInput');
    if (searchInput) {
        searchInput.value = number;
        searchInput.focus();

        // إخفاء النتائج الحالية
        hideSearchResults();

        // تنفيذ البحث
        performHomeSearch(number);

        showNotification(`🔍 جاري البحث عن الطالب رقم ${number}...`, 'info');
    }
}

// بحث جديد
function newSearch() {
    // إخفاء قسم النتيجة
    const resultSection = document.getElementById('resultSection');
    resultSection.style.display = 'none';

    // مسح مربع البحث
    const searchInput = document.getElementById('homeSearchInput');
    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }

    // التمرير إلى أعلى الصفحة
    window.scrollTo({ top: 0, behavior: 'smooth' });

    showNotification('يمكنك البحث عن طالب آخر الآن', 'info');
}

// طباعة النتيجة
function printResult() {
    // إخفاء الأزرار مؤقتاً
    const actions = document.querySelector('.result-actions');
    const originalDisplay = actions.style.display;
    actions.style.display = 'none';

    // طباعة قسم النتيجة فقط
    const resultSection = document.getElementById('resultSection');
    const printContent = resultSection.innerHTML;
    const originalContent = document.body.innerHTML;

    document.body.innerHTML = printContent;
    window.print();
    document.body.innerHTML = originalContent;

    // إعادة تحميل الصفحة لاستعادة الوظائف
    location.reload();
}

// مشاركة النتيجة
function shareResult(studentNumber, studentName, average) {
    const shareText = `🎓 نتيجة ${studentName}\n📋 رقم الطالب: ${studentNumber}\n📊 المعدل: ${average}\n🏆 باكالوريا 2025\n\n${window.location.origin}/student/${studentNumber}/`;

    if (navigator.share) {
        navigator.share({
            title: `نتيجة ${studentName}`,
            text: shareText,
            url: `${window.location.origin}/student/${studentNumber}/`
        }).then(() => {
            showNotification('تم مشاركة النتيجة بنجاح!', 'success');
        }).catch((error) => {
            console.log('خطأ في المشاركة:', error);
            fallbackShare(shareText);
        });
    } else {
        fallbackShare(shareText);
    }
}

// مشاركة احتياطية
function fallbackShare(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ رابط النتيجة! يمكنك مشاركته الآن.', 'success');
        }).catch(() => {
            // إنشاء textarea مؤقت للنسخ
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            showNotification('تم نسخ النص! يمكنك مشاركته الآن.', 'success');
        });
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type}`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// إغلاق النافذة عند النقر خارجها
window.onclick = function(event) {
    const filterModal = document.getElementById('filterModal');
    const rankingModal = document.getElementById('rankingModal');

    if (event.target === filterModal) {
        filterModal.style.display = 'none';
    }
    if (event.target === rankingModal) {
        rankingModal.style.display = 'none';
    }

    // إخفاء نتائج البحث عند النقر خارجها
    const searchResults = document.getElementById('searchResults');
    const searchInput = document.querySelector('input[name="search"]');

    if (searchResults && searchInput &&
        !searchResults.contains(event.target) &&
        !searchInput.contains(event.target)) {
        hideSearchResults();
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const featureCards = document.querySelectorAll('.feature-card');

    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // إعداد مربع البحث في الصفحة الرئيسية
    const homeSearchInput = document.getElementById('homeSearchInput');
    const homeQuickSearchIndicator = document.getElementById('homeQuickSearchIndicator');

    if (homeSearchInput && homeQuickSearchIndicator) {
        // إظهار المؤشر عند التركيز
        homeSearchInput.addEventListener('focus', function() {
            homeQuickSearchIndicator.style.display = 'block';
        });

        // إخفاء المؤشر عند فقدان التركيز (مع تأخير للسماح بالنقر على النتائج)
        homeSearchInput.addEventListener('blur', function() {
            setTimeout(() => {
                homeQuickSearchIndicator.style.display = 'none';
                hideSearchResults();
            }, 200);
        });
    }
});
</script>
{% endblock %}
