{% extends 'base.html' %}

{% block title %}موريباك - نتائج المسابقات الوطنية{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-4 mb-4">
            <i class="fas fa-graduation-cap me-3"></i>
            موريباك
        </h1>
        <p class="lead">الوجهة الأولى والمعتمدة لدى الموريتانيين للحصول على نتائج المسابقات الوطنية</p>

        <!-- مربع البحث السريع -->
        <div class="search-container mt-4">
            <div class="search-box">
                <form method="GET" action="#" id="homeSearchForm" class="d-flex justify-content-center">
                    <div class="input-group" style="max-width: 500px;">
                        <input type="text"
                               name="search"
                               id="homeSearchInput"
                               class="form-control form-control-lg"
                               placeholder="ابحث برقم الطالب أو الاسم..."
                               autocomplete="off">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </form>

                <!-- مؤشر البحث السريع -->
                <div id="homeQuickSearchIndicator" class="quick-search-indicator mt-2" style="display: none;">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        اكتب للبحث السريع في جميع المسابقات أو اضغط Enter للبحث الكامل
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Competitions Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5">نتائج المسابقات الوطنية</h2>
            </div>
        </div>
        
        <div class="row">
            {% for competition in competitions %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card competition-card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-trophy text-warning me-2"></i>
                            {{ competition.name }}
                        </h5>
                        <p class="card-text">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            السنة: {{ competition.year }}
                        </p>
                        <a href="{% url 'competition_detail' competition.slug %}" 
                           class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            عرض النتائج
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد مسابقات متاحة حالياً
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="text-center mb-5">مميزات الموقع</h3>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card text-center h-100 feature-card" data-feature="search">
                    <div class="card-body">
                        <div class="feature-icon">🔍</div>
                        <h5 class="card-title">البحث السريع</h5>
                        <p class="card-text">ابحث عن نتيجتك برقم الطالب بسهولة وسرعة</p>
                        <button class="btn btn-primary btn-sm feature-btn" onclick="activateSearch()">
                            جرب الآن
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card text-center h-100 feature-card" data-feature="filter">
                    <div class="card-body">
                        <div class="feature-icon">🔽</div>
                        <h5 class="card-title">التصفية المتقدمة</h5>
                        <p class="card-text">صفي النتائج حسب الشعبة والولاية والمدرسة</p>
                        <button class="btn btn-success btn-sm feature-btn" onclick="showFilterOptions()">
                            جرب الآن
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card text-center h-100 feature-card" data-feature="ranking">
                    <div class="card-body">
                        <div class="feature-icon">🏆</div>
                        <h5 class="card-title">الترتيب والأوائل</h5>
                        <p class="card-text">اطلع على ترتيب الأوائل في كل شعبة</p>
                        <button class="btn btn-warning btn-sm feature-btn" onclick="showTopStudents()">
                            جرب الآن
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة التصفية المتقدمة -->
        <div id="filterModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔽 التصفية المتقدمة</h3>
                    <span class="close" onclick="closeModal('filterModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>🏫 تصفية حسب المدرسة:</h5>
                            <div id="schoolsList" class="filter-list">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>🗺️ تصفية حسب الولاية:</h5>
                            <div id="wilayasList" class="filter-list">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة الأوائل -->
        <div id="rankingModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🏆 الأوائل في كل شعبة</h3>
                    <span class="close" onclick="closeModal('rankingModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="topStudentsList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.feature-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.feature-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-btn {
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.feature-btn:hover {
    transform: scale(1.05);
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 80%;
    max-width: 800px;
    animation: slideIn 0.3s ease;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: white;
    opacity: 0.8;
}

.close:hover {
    opacity: 1;
}

.filter-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
}

.filter-item {
    padding: 8px 12px;
    margin: 5px 0;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.top-student-card {
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    border-left: 5px solid #fdcb6e;
}

.rank-badge {
    background: #2d3436;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.search-highlight {
    background: #fff3cd;
    border: 2px solid #ffc107;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

/* Search Results Styles */
.search-results-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 5px;
}

.search-results-container.loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.search-results-container.no-results {
    padding: 20px;
    text-align: center;
    color: #999;
}

.search-results-container.error {
    padding: 20px;
    text-align: center;
    color: #dc3545;
}

.search-results-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-results-header h5 {
    margin: 0;
    color: #333;
    font-size: 1rem;
}

.results-count {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.search-result-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-main {
    margin-bottom: 8px;
}

.result-name {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.result-number {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.result-details {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.result-subject,
.result-average {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    color: #495057;
}

.result-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.result-status.status-success {
    background: #d4edda;
    color: #155724;
}

.result-status.status-danger {
    background: #f8d7da;
    color: #721c24;
}

.result-location {
    margin-top: 5px;
}

/* تحسين مربع البحث */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين موضع البحث */
.search-container {
    position: relative;
}

/* إخفاء النتائج عند النقر خارجها */
.search-results-container.hidden {
    display: none;
}

/* تحسين مربع البحث في الصفحة الرئيسية */
.hero-section .search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.hero-section .quick-search-indicator {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px 15px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hero-section .search-results-container {
    margin-top: 5px;
}

/* تحسين تصميم البحث */
.input-group .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group .btn {
    border-left: none;
}

/* تأثير النبضة للبحث النشط */
.search-highlight {
    animation: searchPulse 2s infinite;
}

@keyframes searchPulse {
    0% {
        border-color: #ffc107;
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        border-color: #ffc107;
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}
</style>

<script>
// تفعيل البحث السريع
function activateSearch() {
    // البحث عن مربع البحث في الصفحة الرئيسية
    const searchInput = document.getElementById('homeSearchInput');
    const quickSearchIndicator = document.getElementById('homeQuickSearchIndicator');

    if (searchInput) {
        // تمييز مربع البحث
        searchInput.classList.add('search-highlight');
        searchInput.focus();

        // إظهار مؤشر البحث السريع
        if (quickSearchIndicator) {
            quickSearchIndicator.style.display = 'block';
        }

        // إزالة التمييز بعد 3 ثوان
        setTimeout(() => {
            searchInput.classList.remove('search-highlight');
        }, 3000);

        // التمرير إلى مربع البحث
        searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // إضافة البحث التلقائي أثناء الكتابة
        enableLiveSearch(searchInput);

        // إضافة معالج للنموذج
        const searchForm = document.getElementById('homeSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                performHomeSearch(searchInput.value.trim());
            });
        }
    } else {
        // إذا لم يوجد مربع البحث، أظهر رسالة
        showNotification('مربع البحث غير متاح في هذه الصفحة', 'warning');
    }
}

// تفعيل البحث المباشر
function enableLiveSearch(searchInput) {
    // إزالة المستمع السابق إن وجد
    searchInput.removeEventListener('input', handleLiveSearch);

    // إضافة مستمع جديد
    searchInput.addEventListener('input', handleLiveSearch);

    // إضافة مستمع للضغط على Enter
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            performSearch(this.value.trim());
        }
    });
}

// معالج البحث المباشر
let searchTimeout;
function handleLiveSearch(e) {
    const query = e.target.value.trim();

    // إلغاء البحث السابق
    clearTimeout(searchTimeout);

    // إخفاء النتائج إذا كان النص فارغ
    if (query.length === 0) {
        hideSearchResults();
        return;
    }

    // البحث بعد توقف الكتابة لمدة 500ms
    searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
            performLiveSearch(query);
        }
    }, 500);
}

// تنفيذ البحث المباشر
function performLiveSearch(query) {
    showSearchResults('🔍 جاري البحث في جميع المسابقات...', 'loading');

    fetch(`/api/search/?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.results.length > 0) {
                displaySearchResults(data.results, query);
            } else {
                showSearchResults(`❌ لم يتم العثور على نتائج للبحث: "${query}"<br><small class="text-muted">جرب البحث برقم الطالب أو الاسم الكامل</small>`, 'no-results');
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            showSearchResults('⚠️ خطأ في البحث، يرجى المحاولة مرة أخرى', 'error');
        });
}

// عرض نتائج البحث
function displaySearchResults(results, query) {
    let html = `
        <div class="search-results-header">
            <h5>🔍 نتائج البحث عن: "${query}"</h5>
            <span class="results-count">${results.length} نتيجة</span>
        </div>
    `;

    results.forEach(result => {
        const statusIcon = result.status === 'PASS' ? '✅' : '❌';
        const statusText = result.status === 'PASS' ? 'ناجح' : 'راسب';
        const statusClass = result.status === 'PASS' ? 'success' : 'danger';

        html += `
            <div class="search-result-item" onclick="window.location.href='${result.url}'">
                <div class="result-main">
                    <h6 class="result-name">👤 ${result.student_name}</h6>
                    <p class="result-number">📋 رقم الطالب: ${result.student_number}</p>
                </div>
                <div class="result-details">
                    <span class="result-subject">📚 ${result.subject}</span>
                    <span class="result-average">📊 ${result.average}</span>
                    <span class="result-status status-${statusClass}">${statusIcon} ${statusText}</span>
                </div>
                <div class="result-location">
                    <small class="text-muted">🏫 ${result.school} • 📍 ${result.wilaya}</small>
                </div>
            </div>
        `;
    });

    showSearchResults(html, 'results');
}

// إظهار نتائج البحث
function showSearchResults(content, type) {
    let resultsContainer = document.getElementById('searchResults');

    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'searchResults';
        resultsContainer.className = 'search-results-container';

        const searchInput = document.querySelector('input[name="search"]');
        searchInput.parentNode.appendChild(resultsContainer);
    }

    resultsContainer.className = `search-results-container ${type}`;
    resultsContainer.innerHTML = content;
    resultsContainer.style.display = 'block';
}

// إخفاء نتائج البحث
function hideSearchResults() {
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

// تنفيذ البحث في الصفحة الرئيسية
function performHomeSearch(query) {
    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    // البحث في أحدث مسابقة نشطة
    const competitions = document.querySelectorAll('.competition-card a');
    if (competitions.length > 0) {
        const latestCompetition = competitions[0].href;
        window.location.href = `${latestCompetition}?search=${encodeURIComponent(query)}`;
    } else {
        showNotification('لا توجد مسابقات متاحة للبحث', 'warning');
    }
}

// تنفيذ البحث الكامل
function performSearch(query) {
    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    // الانتقال لصفحة البحث
    const currentUrl = window.location.pathname;
    if (currentUrl === '/') {
        // في الصفحة الرئيسية، استخدم الدالة المخصصة
        performHomeSearch(query);
    } else {
        // في صفحة مسابقة، ابحث في نفس المسابقة
        window.location.href = `${currentUrl}?search=${encodeURIComponent(query)}`;
    }
}

// عرض خيارات التصفية
function showFilterOptions() {
    const modal = document.getElementById('filterModal');
    modal.style.display = 'block';

    // تحميل قائمة المدارس
    loadSchools();

    // تحميل قائمة الولايات
    loadWilayas();
}

// عرض الأوائل
function showTopStudents() {
    const modal = document.getElementById('rankingModal');
    modal.style.display = 'block';

    // تحميل قائمة الأوائل
    loadTopStudents();
}

// إغلاق النافذة
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// تحميل قائمة المدارس
function loadSchools() {
    const schoolsList = document.getElementById('schoolsList');
    schoolsList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    fetch('/api/schools/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                data.schools.forEach(school => {
                    html += `
                        <div class="filter-item" onclick="filterBySchool('${school.name}', '${school.url}')">
                            <div>
                                <span>🏫 ${school.name}</span>
                                <br><small class="text-muted">📍 ${school.wilaya} • ${school.results_count} نتيجة</small>
                            </div>
                            <small class="text-primary">اضغط للتصفية</small>
                        </div>
                    `;
                });

                if (html === '') {
                    html = '<div class="text-center text-muted">لا توجد مدارس متاحة</div>';
                }

                schoolsList.innerHTML = html;
            } else {
                schoolsList.innerHTML = '<div class="text-center text-danger">خطأ في تحميل البيانات</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            schoolsList.innerHTML = '<div class="text-center text-danger">خطأ في الاتصال</div>';
        });
}

// تحميل قائمة الولايات
function loadWilayas() {
    const wilayasList = document.getElementById('wilayasList');
    wilayasList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    fetch('/api/wilayas/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                data.wilayas.forEach(wilaya => {
                    html += `
                        <div class="filter-item" onclick="filterByWilaya('${wilaya.name}', '${wilaya.url}')">
                            <div>
                                <span>🗺️ ${wilaya.name}</span>
                                <br><small class="text-muted">🏫 ${wilaya.schools_count} مدرسة • ${wilaya.results_count} نتيجة</small>
                            </div>
                            <small class="text-primary">اضغط للتصفية</small>
                        </div>
                    `;
                });

                if (html === '') {
                    html = '<div class="text-center text-muted">لا توجد ولايات متاحة</div>';
                }

                wilayasList.innerHTML = html;
            } else {
                wilayasList.innerHTML = '<div class="text-center text-danger">خطأ في تحميل البيانات</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            wilayasList.innerHTML = '<div class="text-center text-danger">خطأ في الاتصال</div>';
        });
}

// تحميل قائمة الأوائل
function loadTopStudents() {
    const topStudentsList = document.getElementById('topStudentsList');
    topStudentsList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الأوائل...</div>';

    // الحصول على slug المسابقة الحالية من URL أو استخدام الافتراضي
    const currentUrl = window.location.pathname;
    const competitionMatch = currentUrl.match(/\/([^\/]+)\//);
    const competitionSlug = competitionMatch ? competitionMatch[1] : '';

    const apiUrl = competitionSlug ? `/api/top-students/?competition=${competitionSlug}` : '/api/top-students/';

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `<h4 class="mb-4">🏆 الأوائل في ${data.competition.name}:</h4>`;

                if (data.top_students.length > 0) {
                    data.top_students.forEach(student => {
                        html += `
                            <div class="top-student-card" onclick="window.open('${student.url}', '_blank')">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-1">👤 ${student.student_name}</h5>
                                        <p class="mb-1">📚 ${student.subject}</p>
                                        <small class="text-muted">
                                            📊 المعدل: ${student.average} •
                                            🏫 ${student.school} •
                                            📍 ${student.wilaya}
                                        </small>
                                    </div>
                                    <div class="rank-badge">
                                        المرتبة ${student.rank}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += '<div class="text-center text-muted">لا توجد نتائج متاحة</div>';
                }

                topStudentsList.innerHTML = html;
            } else {
                topStudentsList.innerHTML = `<div class="text-center text-danger">خطأ: ${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            topStudentsList.innerHTML = '<div class="text-center text-danger">خطأ في الاتصال</div>';
        });
}

// التصفية حسب المدرسة
function filterBySchool(schoolName, schoolUrl) {
    closeModal('filterModal');

    if (schoolUrl && schoolUrl !== 'undefined') {
        window.location.href = schoolUrl;
    } else {
        // البحث عن رابط المدرسة في الصفحة كبديل
        const schoolLinks = document.querySelectorAll('a[href*="school"]');
        let found = false;

        schoolLinks.forEach(link => {
            if (link.textContent.includes(schoolName)) {
                window.location.href = link.href;
                found = true;
            }
        });

        if (!found) {
            showNotification(`🔍 لم يتم العثور على نتائج للمدرسة: ${schoolName}`, 'warning');
        }
    }
}

// التصفية حسب الولاية
function filterByWilaya(wilayaName, wilayaUrl) {
    closeModal('filterModal');

    if (wilayaUrl && wilayaUrl !== 'undefined') {
        window.location.href = wilayaUrl;
    } else {
        // البحث عن رابط الولاية في الصفحة كبديل
        const wilayaLinks = document.querySelectorAll('a[href*="wilaya"]');
        let found = false;

        wilayaLinks.forEach(link => {
            if (link.textContent.includes(wilayaName)) {
                window.location.href = link.href;
                found = true;
            }
        });

        if (!found) {
            showNotification(`🔍 لم يتم العثور على نتائج للولاية: ${wilayaName}`, 'warning');
        }
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type}`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// إغلاق النافذة عند النقر خارجها
window.onclick = function(event) {
    const filterModal = document.getElementById('filterModal');
    const rankingModal = document.getElementById('rankingModal');

    if (event.target === filterModal) {
        filterModal.style.display = 'none';
    }
    if (event.target === rankingModal) {
        rankingModal.style.display = 'none';
    }

    // إخفاء نتائج البحث عند النقر خارجها
    const searchResults = document.getElementById('searchResults');
    const searchInput = document.querySelector('input[name="search"]');

    if (searchResults && searchInput &&
        !searchResults.contains(event.target) &&
        !searchInput.contains(event.target)) {
        hideSearchResults();
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const featureCards = document.querySelectorAll('.feature-card');

    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // إعداد مربع البحث في الصفحة الرئيسية
    const homeSearchInput = document.getElementById('homeSearchInput');
    const homeQuickSearchIndicator = document.getElementById('homeQuickSearchIndicator');

    if (homeSearchInput && homeQuickSearchIndicator) {
        // إظهار المؤشر عند التركيز
        homeSearchInput.addEventListener('focus', function() {
            homeQuickSearchIndicator.style.display = 'block';
        });

        // إخفاء المؤشر عند فقدان التركيز (مع تأخير للسماح بالنقر على النتائج)
        homeSearchInput.addEventListener('blur', function() {
            setTimeout(() => {
                homeQuickSearchIndicator.style.display = 'none';
                hideSearchResults();
            }, 200);
        });
    }
});
</script>
{% endblock %}
