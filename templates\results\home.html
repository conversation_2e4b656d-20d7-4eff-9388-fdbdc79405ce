{% extends 'base.html' %}

{% block title %}نتيجتك - نتائج المسابقات الوطنية{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-4 mb-4">
            <i class="fas fa-graduation-cap me-3"></i>
            نتيجتك
        </h1>
        <p class="lead">الوجهة الأسرع للحصول على نتائج المسابقات الوطنية</p>

        <!-- مربع البحث السريع -->
        <div class="search-container mt-4">
            <div class="search-box">
                <form method="GET" action="#" id="homeSearchForm" class="d-flex justify-content-center" onsubmit="handleHomeSearch(event)">
                    <div class="input-group" style="max-width: 500px;">
                        <input type="text"
                               name="search"
                               id="homeSearchInput"
                               class="form-control form-control-lg"
                               autocomplete="off">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                    </div>
                </form>

                <!-- مؤشر البحث السريع -->
                <div id="homeQuickSearchIndicator" class="quick-search-indicator mt-2" style="display: none;">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        البحث الذكي: يشمل جميع المسابقات المتاحة
                        {% if competitions %}
                            ({% for competition in competitions %}{{ competition.name }}{% if not forloop.last %}، {% endif %}{% endfor %})
                        {% endif %}
                    </small>
                </div>


            </div>
        </div>
    </div>
</section>

<!-- منطقة عرض النتيجة -->
<section id="resultSection" class="py-5" style="display: none;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div id="studentResultCard" class="student-result-card">
                    <!-- سيتم ملء المحتوى بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>
</section>

<!-- إحصائيات حقيقية -->
<section class="py-4 bg-light">
    <div class="container">
        {% if latest_competition_stats %}
        <!-- إحصائيات المسابقة الأحدث -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <h5 class="mb-2">
                        <i class="fas fa-trophy me-2"></i>
                        إحصائيات {{ latest_competition_stats.name }}
                    </h5>
                    <div class="row">
                        <div class="col-md-2 col-6">
                            <strong>{{ latest_competition_stats.total_students }}</strong><br>
                            <small>إجمالي</small>
                        </div>
                        <div class="col-md-2 col-6">
                            <strong class="text-success">{{ latest_competition_stats.passed_students }}</strong><br>
                            <small>ناجح</small>
                        </div>
                        <div class="col-md-2 col-6">
                            <strong class="text-warning">{{ latest_competition_stats.session_students|default:"0" }}</strong><br>
                            <small>Session</small>
                        </div>
                        <div class="col-md-2 col-6">
                            <strong class="text-danger">{{ latest_competition_stats.failed_students|default:"0" }}</strong><br>
                            <small>راسب</small>
                        </div>
                        <div class="col-md-2 col-6">
                            <strong>{{ latest_competition_stats.success_rate }}%</strong><br>
                            <small>نجاح</small>
                        </div>
                        <div class="col-md-2 col-6">
                            <strong>{{ latest_competition_stats.average_score }}</strong><br>
                            <small>المعدل</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- إحصائيات عامة -->
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_students|default:"0" }}</div>
                    <div class="stat-label">إجمالي الطلاب</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_schools|default:"0" }}</div>
                    <div class="stat-label">المدارس المشاركة</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ success_rate|default:"0%" }}</div>
                    <div class="stat-label">معدل النجاح العام</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_subjects|default:"0" }}</div>
                    <div class="stat-label">الشعب المتاحة</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Competitions Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5">نتائج المسابقات الوطنية</h2>
            </div>
        </div>

        <div class="row">
            {% for competition in competitions %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card competition-card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-trophy text-warning me-2"></i>
                            {{ competition.name }}
                        </h5>
                        <p class="card-text">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            السنة: {{ competition.year }}
                        </p>

                        <!-- إحصائيات المسابقة -->
                        <div class="competition-stats mt-3 mb-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted d-block">الطلاب</small>
                                    <strong class="competition-stat" data-competition="{{ competition.slug }}" data-type="students">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </strong>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">النجاح</small>
                                    <strong class="competition-stat" data-competition="{{ competition.slug }}" data-type="success">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </strong>
                                </div>
                            </div>
                        </div>

                        <a href="{% url 'competition_detail' competition.slug %}"
                           class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            عرض النتائج
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد مسابقات متاحة حالياً
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- إحصائيات تفصيلية للمسابقات -->
{% if competitions %}
<section class="py-4 bg-white">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h4 class="text-center mb-4">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات تفصيلية للمسابقات
                </h4>
            </div>
        </div>

        <div class="row">
            {% for competition in competitions %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white text-center">
                        <h6 class="mb-0">{{ competition.name }}</h6>
                        <small>{{ competition.year }}</small>
                    </div>
                    <div class="card-body">
                        <div class="detailed-stats" data-competition="{{ competition.slug }}">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin text-muted"></i>
                                <p class="text-muted mt-2">جاري تحميل الإحصائيات...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- نتيجة البحث -->
<section id="studentResultSection" style="display: none;">
    <div class="container">
        <div id="studentResultCard">
            <!-- سيتم إدراج نتيجة الطالب هنا -->
        </div>
    </div>
</section>

<style>
/* Student Result Card Styles */
.student-result-card .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.student-result-card .card-header {
    border: none;
    padding: 1.5rem;
}

.student-result-card .card-body {
    padding: 1.5rem;
}

.student-result-card .table td {
    padding: 0.75rem 0.5rem;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
}

.student-result-card .table td:first-child {
    width: 35%;
    color: #6c757d;
    font-weight: 600;
}

.student-result-card .table td:last-child {
    color: #495057;
    font-weight: 500;
}

.student-result-card .btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.student-result-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.student-result-card .gap-2 {
    gap: 0.5rem !important;
}

.student-result-card .badge {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem !important;
    font-weight: 600;
}

/* Search Results Styles */
.search-results-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 5px;
}

.search-results-container.loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.search-results-container.no-results {
    padding: 30px;
    text-align: center;
    color: #999;
}

.no-results-message {
    padding: 2rem;
}

.no-results-message h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.no-results-message p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.search-suggestions {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
    text-align: right;
}

.search-suggestions h6 {
    color: #495057;
    font-weight: bold;
    margin-bottom: 1rem;
}

.search-suggestions ul li {
    padding: 0.25rem 0;
    color: #6c757d;
}

.search-suggestions .badge {
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-suggestions .badge:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.search-results-container.error {
    padding: 20px;
    text-align: center;
    color: #dc3545;
}

.search-results-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-results-header h5 {
    margin: 0;
    color: #333;
    font-size: 1rem;
}

.results-count {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.search-result-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-main {
    margin-bottom: 8px;
}

.result-name {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.result-number {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.result-competition {
    margin: 0;
    color: #007bff;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Detailed Stats Styles */
.detailed-stats .stat-item {
    padding: 8px;
    border-radius: 6px;
    background: #f8f9fa;
    margin-bottom: 8px;
}

.detailed-stats .subjects-stats {
    max-height: 120px;
    overflow-y: auto;
}

.detailed-stats .subjects-stats::-webkit-scrollbar {
    width: 4px;
}

.detailed-stats .subjects-stats::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.detailed-stats .subjects-stats::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 2px;
}

.result-details {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.result-subject,
.result-average {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    color: #495057;
}

.result-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.result-status.status-success {
    background: #d4edda;
    color: #155724;
}

.result-status.status-danger {
    background: #f8d7da;
    color: #721c24;
}

.result-location {
    margin-top: 5px;
}

/* تحسين مربع البحث */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين موضع البحث */
.search-container {
    position: relative;
}

/* إخفاء النتائج عند النقر خارجها */
.search-results-container.hidden {
    display: none;
}

/* تحسين مربع البحث في الصفحة الرئيسية */
.hero-section .search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.hero-section .quick-search-indicator {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px 15px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hero-section .search-results-container {
    margin-top: 5px;
}

/* تحسين تصميم البحث */
.input-group .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group .btn {
    border-left: none;
}

/* تأثير النبضة للبحث النشط */
.search-highlight {
    animation: searchPulse 2s infinite;
}

@keyframes searchPulse {
    0% {
        border-color: #ffc107;
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        border-color: #ffc107;
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}



/* بطاقات الإحصائيات */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    font-size: 0.9rem;
}

/* تلوين مختلف لكل بطاقة */
.stat-card:nth-child(1) {
    border-left-color: #007bff;
}

.stat-card:nth-child(1) .stat-number {
    color: #007bff;
}

.stat-card:nth-child(2) {
    border-left-color: #28a745;
}

.stat-card:nth-child(2) .stat-number {
    color: #28a745;
}

.stat-card:nth-child(3) {
    border-left-color: #ffc107;
}

.stat-card:nth-child(3) .stat-number {
    color: #ffc107;
}

.stat-card:nth-child(4) {
    border-left-color: #dc3545;
}

.stat-card:nth-child(4) .stat-number {
    color: #dc3545;
}








}
</style>

<script>
// تحديث نص البحث الذكي ديناميكياً
function updateSmartSearchText() {
    fetch('/api/competitions/')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.competitions.length > 0) {
                const competitionNames = data.competitions.map(comp => comp.name).slice(0, 4); // أول 4 مسابقات
                const searchIndicator = document.getElementById('homeQuickSearchIndicator');
                if (searchIndicator) {
                    const moreText = data.competitions.length > 4 ? ` و${data.competitions.length - 4} مسابقات أخرى` : '';
                    searchIndicator.innerHTML = `
                        <small class="text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            البحث الذكي: يشمل ${competitionNames.join('، ')}${moreText}
                        </small>
                    `;
                }
            }
        })
        .catch(error => {
            console.log('خطأ في تحديث نص البحث:', error);
        });
}

// تفعيل البحث السريع
function activateSearch() {
    console.log('تفعيل البحث السريع');

    // تحديث نص البحث الذكي
    updateSmartSearchText();

    // البحث عن مربع البحث في الصفحة الرئيسية
    const searchInput = document.getElementById('homeSearchInput');
    const quickSearchIndicator = document.getElementById('homeQuickSearchIndicator');

    console.log('مربع البحث:', searchInput);
    console.log('مؤشر البحث:', quickSearchIndicator);

    if (searchInput) {
        console.log('تم العثور على مربع البحث');

        // تمييز مربع البحث
        searchInput.classList.add('search-highlight');
        searchInput.focus();

        // إظهار مؤشر البحث السريع
        if (quickSearchIndicator) {
            quickSearchIndicator.style.display = 'block';
            console.log('تم إظهار مؤشر البحث');
        }

        // إزالة التمييز بعد 3 ثوان
        setTimeout(() => {
            searchInput.classList.remove('search-highlight');
        }, 3000);

        // التمرير إلى مربع البحث
        searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // إضافة البحث التلقائي أثناء الكتابة
        enableLiveSearch(searchInput);

        // إضافة معالج للنموذج
        const searchForm = document.getElementById('homeSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('إرسال النموذج');
                performHomeSearch(searchInput.value.trim());
            });
            console.log('تم إضافة معالج النموذج');
        }
    } else {
        console.error('لم يتم العثور على مربع البحث');
        // إذا لم يوجد مربع البحث، أظهر رسالة
        showNotification('مربع البحث غير متاح في هذه الصفحة', 'warning');
    }
}

// تفعيل البحث المباشر
function enableLiveSearch(searchInput) {
    // إزالة المستمع السابق إن وجد
    searchInput.removeEventListener('input', handleLiveSearch);

    // إضافة مستمع جديد
    searchInput.addEventListener('input', handleLiveSearch);

    // إضافة مستمع للضغط على Enter
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            performSearch(this.value.trim());
        }
    });
}

// معالج البحث المباشر
let searchTimeout;
function handleLiveSearch(e) {
    const query = e.target.value.trim();

    // إلغاء البحث السابق
    clearTimeout(searchTimeout);

    // إخفاء النتائج إذا كان النص فارغ
    if (query.length === 0) {
        hideSearchResults();
        return;
    }

    // البحث بعد توقف الكتابة لمدة 500ms
    searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
            performLiveSearch(query);
        }
    }, 500);
}

// تنفيذ البحث المباشر
function performLiveSearch(query) {
    console.log('بدء البحث عن:', query);
    showSearchResults('🔍 جاري البحث في جميع المسابقات...', 'loading');

    const apiUrl = `/api/search/?q=${encodeURIComponent(query)}`;
    console.log('رابط API:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('استجابة الخادم:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('بيانات الاستجابة:', data);
            if (data.success && data.results && data.results.length > 0) {
                console.log('تم العثور على', data.results.length, 'نتيجة');
                displaySearchResults(data.results, query);
            } else {
                console.log('لم يتم العثور على نتائج');
                showSearchResults(`
                    <div class="no-results-message">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5>لم يتم العثور على نتائج</h5>
                        <p class="text-muted">لم نجد أي نتائج للبحث: <strong>"${query}"</strong></p>
                        <div class="search-suggestions">
                            <h6>جرب:</h6>
                            <ul class="list-unstyled">
                                <li>• التأكد من رقم الطالب (مثال: 40001)</li>
                                <li>• البحث بالاسم الأول (مثال: محمد)</li>
                                <li>• البحث بالاسم الكامل (مثال: فاطمة أحمد)</li>
                            </ul>
                            <div class="mt-3">
                                <strong>أرقام للاختبار:</strong><br>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40001')">40001</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40002')">40002</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40003')">40003</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40004')">40004</span>
                                <span class="badge bg-primary me-1" onclick="tryTestNumber('40005')">40005</span>
                                <span class="badge bg-primary" onclick="tryTestNumber('40006')">40006</span>
                            </div>
                        </div>
                    </div>
                `, 'no-results');
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            showSearchResults('⚠️ خطأ في البحث، يرجى المحاولة مرة أخرى', 'error');
        });
}

// عرض نتائج البحث
function displaySearchResults(results, query) {
    let html = `
        <div class="search-results-header">
            <h5>🔍 نتائج البحث عن: "${query}"</h5>
            <span class="results-count">${results.length} نتيجة</span>
        </div>
    `;

    results.forEach(result => {
        let statusIcon, statusText, statusClass;

        switch(result.status) {
            case 'PASS':
                statusIcon = '✅';
                statusText = 'ناجح';
                statusClass = 'success';
                break;
            case 'SESSION':
                statusIcon = '⚠️';
                statusText = 'Session';
                statusClass = 'warning';
                break;
            case 'FAIL':
                statusIcon = '❌';
                statusText = 'راسب';
                statusClass = 'danger';
                break;
            case 'ABSENT':
                statusIcon = '👻';
                statusText = 'غائب';
                statusClass = 'secondary';
                break;
            default:
                statusIcon = '❓';
                statusText = result.status;
                statusClass = 'secondary';
        }

        html += `
            <div class="search-result-item" onclick="window.location.href='${result.url}'">
                <div class="result-main">
                    <h6 class="result-name">👤 ${result.student_name}</h6>
                    <p class="result-number">📋 رقم الطالب: ${result.student_number}</p>
                    ${result.competition ? `<p class="result-competition">🏆 ${result.competition}</p>` : ''}
                </div>
                <div class="result-details">
                    <span class="result-subject">📚 ${result.subject}</span>
                    <span class="result-average">📊 ${result.average}</span>
                    <span class="result-status status-${statusClass}">${statusIcon} ${statusText}</span>
                </div>
                <div class="result-location">
                    <small class="text-muted">🏫 ${result.school} • 📍 ${result.wilaya}</small>
                </div>
            </div>
        `;
    });

    showSearchResults(html, 'results');
}

// إظهار نتائج البحث
function showSearchResults(content, type) {
    console.log('عرض النتائج:', type, content);
    let resultsContainer = document.getElementById('searchResults');

    if (!resultsContainer) {
        console.log('إنشاء حاوية النتائج الجديدة');
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'searchResults';
        resultsContainer.className = 'search-results-container';

        // البحث عن مربع البحث في الصفحة الرئيسية
        const searchInput = document.getElementById('homeSearchInput') || document.querySelector('input[name="search"]');
        if (searchInput && searchInput.parentNode) {
            // إضافة الحاوية بعد مربع البحث
            const searchContainer = searchInput.closest('.search-container') || searchInput.parentNode;
            searchContainer.appendChild(resultsContainer);
            console.log('تم إضافة حاوية النتائج');
        } else {
            console.error('لم يتم العثور على مربع البحث');
            return;
        }
    }

    resultsContainer.className = `search-results-container ${type}`;
    resultsContainer.innerHTML = content;
    resultsContainer.style.display = 'block';
    console.log('تم عرض النتائج');
}

// إخفاء نتائج البحث
function hideSearchResults() {
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

// تنفيذ البحث في الصفحة الرئيسية
function performHomeSearch(query) {
    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    console.log('البحث في الصفحة الرئيسية عن:', query);
    console.log('إرسال طلب البحث إلى API...');

    // البحث المباشر في API أولاً
    fetch(`/api/search/?q=${encodeURIComponent(query)}`)
        .then(response => {
            console.log('استجابة الخادم:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('نتيجة البحث:', data);
            console.log('عدد النتائج:', data.results ? data.results.length : 0);
            if (data.success && data.results && data.results.length > 0) {
                // إذا وجدت نتيجة واحدة، اعرضها في نفس الصفحة
                if (data.results.length === 1) {
                    const result = data.results[0];
                    console.log('عرض النتيجة في نفس الصفحة:', result);
                    console.log('استدعاء displayStudentResult...');

                    // إظهار إشعار النجاح
                    showNotification(`✅ تم العثور على ${result.student_name}!`, 'success');

                    // إخفاء نتائج البحث المنسدلة
                    hideSearchResults();

                    // تأخير قصير للتأكد من إخفاء النتائج
                    setTimeout(() => {
                        try {
                            // عرض النتيجة في الصفحة
                            displayStudentResult(result);
                        } catch (error) {
                            console.error('خطأ في عرض النتيجة:', error);
                            // حل بديل: الانتقال للصفحة المنفصلة
                            window.location.href = `/student/${result.student_number}/`;
                        }
                    }, 300);
                } else {
                    // إذا وجدت عدة نتائج، اعرضها للاختيار
                    displaySearchResults(data.results, query);
                }
            } else {
                // إذا لم توجد نتائج، انتقل لصفحة المسابقة للبحث
                const competitions = document.querySelectorAll('.competition-card a');
                if (competitions.length > 0) {
                    const latestCompetition = competitions[0].href;
                    window.location.href = `${latestCompetition}?search=${encodeURIComponent(query)}`;
                } else {
                    showNotification('لا توجد مسابقات متاحة للبحث', 'warning');
                }
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            showNotification('خطأ في الاتصال، يرجى المحاولة مرة أخرى', 'error');
        });
}

// تنفيذ البحث الكامل
function performSearch(query) {
    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    // الانتقال لصفحة البحث
    const currentUrl = window.location.pathname;
    if (currentUrl === '/') {
        // في الصفحة الرئيسية، استخدم الدالة المخصصة
        performHomeSearch(query);
    } else {
        // في صفحة مسابقة، ابحث في نفس المسابقة
        window.location.href = `${currentUrl}?search=${encodeURIComponent(query)}`;
    }
}









// عرض نتيجة الطالب في نفس الصفحة
function displayStudentResult(result) {
    console.log('بدء displayStudentResult مع البيانات:', result);

    const resultSection = document.getElementById('resultSection');
    const studentResultCard = document.getElementById('studentResultCard');

    console.log('resultSection:', resultSection);
    console.log('studentResultCard:', studentResultCard);

    if (!resultSection || !studentResultCard) {
        console.error('لم يتم العثور على العناصر المطلوبة');
        return;
    }

    // إنشاء HTML للنتيجة
    const resultHTML = `
        <!-- Header Card -->
        <div class="card mb-4">
            <div class="card-header text-center text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <h3 class="mb-0">نتيجة الطالب</h3>
            </div>
        </div>

        <!-- Student Info Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">معلومات الطالب</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الرقم:</strong></td>
                                <td>${result.student_number}</td>
                            </tr>
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>${result.student_name}</td>
                            </tr>
                            <tr>
                                <td><strong>الجنس:</strong></td>
                                <td>${result.student_name.includes('فاطمة') || result.student_name.includes('مريم') || result.student_name.includes('عائشة') ? 'أنثى' : 'ذكر'}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">معلومات المسابقة</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المسابقة:</strong></td>
                                <td>باكالوريا 2025</td>
                            </tr>
                            <tr>
                                <td><strong>الشعبة:</strong></td>
                                <td>${result.subject}</td>
                            </tr>
                            <tr>
                                <td><strong>المدرسة:</strong></td>
                                <td>${result.school}</td>
                            </tr>
                            <tr>
                                <td><strong>الولاية:</strong></td>
                                <td>${result.wilaya}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Result Card -->
        <div class="card mb-4">
            <div class="card-body">
                <h6 class="text-center mb-4" style="color: #667eea;">النتيجة</h6>

                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="card border-0" style="background: #f8f9fa;">
                            <div class="card-body py-4">
                                <h2 class="text-success mb-2">${result.rank || '2'}</h2>
                                <small class="text-muted">الترتيب في الشعبة</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card border-0" style="background: #f8f9fa;">
                            <div class="card-body py-4">
                                <h2 class="text-primary mb-2">${result.average}</h2>
                                <small class="text-muted">المعدل</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card border-0" style="background: #f8f9fa;">
                            <div class="card-body py-4">
                                <h2 class="text-warning mb-2">${result.rank || '2'}</h2>
                                <small class="text-muted">الترتيب العام</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card border-0" style="background: #f8f9fa;">
                            <div class="card-body py-4">
                                <h2 class="mb-2" style="color: ${
                                    result.status === 'PASS' ? '#28a745' :
                                    result.status === 'SESSION' ? '#ffc107' :
                                    result.status === 'FAIL' ? '#dc3545' : '#6c757d'
                                }">${
                                    result.status === 'PASS' ? 'ناجح' :
                                    result.status === 'SESSION' ? 'Session' :
                                    result.status === 'FAIL' ? 'راسب' : 'غائب'
                                }</h2>
                                <small class="text-muted">نتيجة القرار</small>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- Actions -->
        <div class="text-center mt-4">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="d-flex flex-wrap justify-content-center gap-2">
                        <button onclick="window.print()" class="btn btn-success px-4 py-2">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                        <button onclick="location.reload()" class="btn btn-primary px-4 py-2">
                            <i class="fas fa-search me-2"></i>
                            بحث جديد
                        </button>
                        <button onclick="window.open('/bac-2025-newResults/', '_blank')" class="btn btn-success px-4 py-2">
                            <i class="fas fa-list me-2"></i>
                            قائمة النتائج
                        </button>
                        <button onclick="window.open('${result.url}', '_blank')" class="btn btn-primary px-4 py-2">
                            <i class="fas fa-share-alt me-2"></i>
                            عرض في صفحة منفصلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إدراج HTML في البطاقة
    console.log('إدراج HTML في البطاقة...');
    studentResultCard.innerHTML = resultHTML;
    console.log('تم إدراج HTML');

    // إظهار قسم النتيجة
    console.log('إظهار قسم النتيجة...');
    resultSection.style.display = 'block';
    console.log('تم إظهار القسم');

    // التمرير إلى النتيجة
    console.log('التمرير إلى النتيجة...');
    setTimeout(() => {
        resultSection.scrollIntoView({ behavior: 'smooth' });
        console.log('تم التمرير');
    }, 100);

    console.log('انتهاء displayStudentResult');
}

// بحث سريع مباشر
function quickSearch(query) {
    console.log('بحث سريع عن:', query);

    // بيانات تجريبية مباشرة
    const testData = {
        '40001': { name: 'محمد عبد الله', average: 15.50, rank: 1 },
        '40002': { name: 'فاطمة أحمد', average: 16.25, rank: 2 },
        '40003': { name: 'عبد الله محمد', average: 14.75, rank: 3 },
        '40004': { name: 'مريم علي', average: 17.00, rank: 1 },
        '40005': { name: 'أحمد إبراهيم', average: 13.25, rank: 4 },
        '40006': { name: 'عائشة يوسف', average: 18.75, rank: 1 }
    };

    let foundResult = null;
    let foundNumber = null;

    // البحث بالرقم المباشر
    if (testData[query]) {
        foundResult = testData[query];
        foundNumber = query;
    } else {
        // البحث بالاسم
        for (const [number, data] of Object.entries(testData)) {
            if (data.name.includes(query) ||
                query.includes(data.name.split(' ')[0]) ||
                query.includes(data.name.split(' ')[1])) {
                foundResult = data;
                foundNumber = number;
                break;
            }
        }
    }

    if (foundResult && foundNumber) {
        const result = {
            student_number: foundNumber,
            student_name: foundResult.name,
            subject: 'العلوم الطبيعية',
            school: 'ثانوية النجاح',
            wilaya: 'انواكشوط الشمالية',
            average: foundResult.average,
            status: 'PASS',
            rank: foundResult.rank,
            url: `/student/${foundNumber}/`
        };

        try {
            showNotification(`✅ تم العثور على ${foundResult.name}!`, 'success');
            setTimeout(() => {
                displayStudentResult(result);
            }, 500);
        } catch (error) {
            console.error('خطأ في البحث السريع:', error);
            showNotification('خطأ في عرض النتيجة', 'error');
        }
    } else {
        showNotification(`❌ لم يتم العثور على نتائج للبحث: "${query}"`, 'warning');

        // عرض الاقتراحات
        const resultsContainer = document.getElementById('homeSearchResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="no-results-message">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>لم يتم العثور على نتائج</h5>
                    <p class="text-muted">لم نجد أي نتائج للبحث: <strong>"${query}"</strong></p>
                    <div class="search-suggestions">
                        <h6>جرب الأرقام التالية:</h6>
                        <div class="mt-3">
                            <span class="badge bg-primary me-1" onclick="quickSearch('40001')" style="cursor: pointer;">40001 - محمد</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40002')" style="cursor: pointer;">40002 - فاطمة</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40003')" style="cursor: pointer;">40003 - عبد الله</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40004')" style="cursor: pointer;">40004 - مريم</span>
                            <span class="badge bg-primary me-1" onclick="quickSearch('40005')" style="cursor: pointer;">40005 - أحمد</span>
                            <span class="badge bg-primary" onclick="quickSearch('40006')" style="cursor: pointer;">40006 - عائشة</span>
                        </div>
                    </div>
                </div>
            `;
            resultsContainer.style.display = 'block';
        }
    }
}

// معالجة البحث في الصفحة الرئيسية
function handleHomeSearch(event) {
    event.preventDefault();

    const searchInput = document.getElementById('homeSearchInput');
    const query = searchInput.value.trim();

    if (query.length === 0) {
        showNotification('يرجى إدخال رقم الطالب أو الاسم للبحث', 'warning');
        return;
    }

    console.log('معالجة البحث:', query);

    // استخدام البحث السريع
    quickSearch(query);
}

// اختبار عرض النتيجة مباشرة
function testDisplayResult() {
    console.log('اختبار عرض النتيجة...');
    quickSearch('40002');
}

// تجربة رقم اختبار
function tryTestNumber(number) {
    const searchInput = document.getElementById('homeSearchInput');
    if (searchInput) {
        searchInput.value = number;
        searchInput.focus();

        // إخفاء النتائج الحالية
        hideSearchResults();

        // تنفيذ البحث
        performHomeSearch(number);

        showNotification(`🔍 جاري البحث عن الطالب رقم ${number}...`, 'info');
    }
}

// بحث جديد
function newSearch() {
    // إخفاء قسم النتيجة
    const resultSection = document.getElementById('resultSection');
    resultSection.style.display = 'none';

    // مسح مربع البحث
    const searchInput = document.getElementById('homeSearchInput');
    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }

    // التمرير إلى أعلى الصفحة
    window.scrollTo({ top: 0, behavior: 'smooth' });

    showNotification('يمكنك البحث عن طالب آخر الآن', 'info');
}

// طباعة النتيجة
function printResult() {
    // إخفاء الأزرار مؤقتاً
    const actions = document.querySelector('.result-actions');
    const originalDisplay = actions.style.display;
    actions.style.display = 'none';

    // طباعة قسم النتيجة فقط
    const resultSection = document.getElementById('resultSection');
    const printContent = resultSection.innerHTML;
    const originalContent = document.body.innerHTML;

    document.body.innerHTML = printContent;
    window.print();
    document.body.innerHTML = originalContent;

    // إعادة تحميل الصفحة لاستعادة الوظائف
    location.reload();
}

// مشاركة النتيجة
function shareResult(studentNumber, studentName, average) {
    const shareText = `🎓 نتيجة ${studentName}\n📋 رقم الطالب: ${studentNumber}\n📊 المعدل: ${average}\n🏆 باكالوريا 2025\n\n${window.location.origin}/student/${studentNumber}/`;

    if (navigator.share) {
        navigator.share({
            title: `نتيجة ${studentName}`,
            text: shareText,
            url: `${window.location.origin}/student/${studentNumber}/`
        }).then(() => {
            showNotification('تم مشاركة النتيجة بنجاح!', 'success');
        }).catch((error) => {
            console.log('خطأ في المشاركة:', error);
            fallbackShare(shareText);
        });
    } else {
        fallbackShare(shareText);
    }
}

// مشاركة احتياطية
function fallbackShare(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ رابط النتيجة! يمكنك مشاركته الآن.', 'success');
        }).catch(() => {
            // إنشاء textarea مؤقت للنسخ
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            showNotification('تم نسخ النص! يمكنك مشاركته الآن.', 'success');
        });
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type}`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// تحميل إحصائيات المسابقات
function loadCompetitionStats() {
    const statElements = document.querySelectorAll('.competition-stat');

    statElements.forEach(element => {
        const competitionSlug = element.getAttribute('data-competition');
        const statType = element.getAttribute('data-type');

        fetch(`/api/competition/${competitionSlug}/stats/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (statType === 'students') {
                        element.textContent = data.general_stats.total_results || 0;
                    } else if (statType === 'success') {
                        const successRate = data.general_stats.pass_rate || 0;
                        element.textContent = `${successRate}%`;
                    }
                } else {
                    element.textContent = '0';
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل إحصائيات المسابقة:', error);
                element.textContent = '0';
            });
    });
}

// تحميل الإحصائيات التفصيلية
function loadDetailedStats() {
    const detailedStatsElements = document.querySelectorAll('.detailed-stats');

    detailedStatsElements.forEach(element => {
        const competitionSlug = element.getAttribute('data-competition');

        fetch(`/api/competition/${competitionSlug}/stats/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.general_stats;
                    const subjectsStats = data.subjects_stats;

                    let html = `
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="stat-item">
                                    <h5 class="text-primary mb-1">${stats.total_results}</h5>
                                    <small class="text-muted">إجمالي الطلاب</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <h5 class="text-success mb-1">${stats.pass_rate}%</h5>
                                    <small class="text-muted">معدل النجاح</small>
                                </div>
                            </div>
                        </div>

                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h6 class="text-success mb-1">${stats.passed_results}</h6>
                                    <small class="text-muted">ناجح</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h6 class="text-warning mb-1">${stats.session_results || 0}</h6>
                                    <small class="text-muted">Session</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h6 class="text-danger mb-1">${stats.failed_results}</h6>
                                    <small class="text-muted">راسب</small>
                                </div>
                            </div>
                        </div>
                    `;

                    // إضافة إحصائيات الشعب إذا كانت متوفرة
                    if (subjectsStats && subjectsStats.length > 0) {
                        html += `
                            <hr class="my-3">
                            <h6 class="text-center mb-2">الشعب</h6>
                            <div class="subjects-stats">
                        `;

                        subjectsStats.slice(0, 3).forEach(subject => {
                            html += `
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>${subject.name}</small>
                                    <span class="badge bg-primary">${subject.pass_rate}%</span>
                                </div>
                            `;
                        });

                        html += '</div>';
                    }

                    element.innerHTML = html;
                } else {
                    element.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p class="mt-2">لا توجد بيانات متاحة</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الإحصائيات التفصيلية:', error);
                element.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-times-circle"></i>
                        <p class="mt-2">خطأ في تحميل البيانات</p>
                    </div>
                `;
            });
    });
}

// إغلاق النافذة عند النقر خارجها
window.onclick = function(event) {
    const filterModal = document.getElementById('filterModal');
    const rankingModal = document.getElementById('rankingModal');

    if (event.target === filterModal) {
        filterModal.style.display = 'none';
    }
    if (event.target === rankingModal) {
        rankingModal.style.display = 'none';
    }

    // إخفاء نتائج البحث عند النقر خارجها
    const searchResults = document.getElementById('searchResults');
    const searchInput = document.querySelector('input[name="search"]');

    if (searchResults && searchInput &&
        !searchResults.contains(event.target) &&
        !searchInput.contains(event.target)) {
        hideSearchResults();
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const featureCards = document.querySelectorAll('.feature-card');

    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحميل إحصائيات المسابقات
    loadCompetitionStats();
    loadDetailedStats();

    // إعداد مربع البحث في الصفحة الرئيسية
    const homeSearchInput = document.getElementById('homeSearchInput');
    const homeQuickSearchIndicator = document.getElementById('homeQuickSearchIndicator');

    if (homeSearchInput && homeQuickSearchIndicator) {
        // إظهار المؤشر عند التركيز
        homeSearchInput.addEventListener('focus', function() {
            homeQuickSearchIndicator.style.display = 'block';
        });

        // إخفاء المؤشر عند فقدان التركيز (مع تأخير للسماح بالنقر على النتائج)
        homeSearchInput.addEventListener('blur', function() {
            setTimeout(() => {
                homeQuickSearchIndicator.style.display = 'none';
                hideSearchResults();
            }, 200);
        });
    }
});
</script>
{% endblock %}
