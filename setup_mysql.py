#!/usr/bin/env python
"""
سكريبت لإعداد MySQL وتشغيل موقع موريباك
"""
import os
import sys
import subprocess
import getpass

def run_command(command, description, check=True):
    """تشغيل أمر مع عرض الوصف"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - تم بنجاح")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"❌ خطأ في {description}")
            if result.stderr:
                print(f"خطأ: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في {description}")
        if e.stderr:
            print(f"خطأ: {e.stderr}")
        return False

def check_mysql():
    """التحقق من وجود MySQL"""
    print("🔍 التحقق من MySQL...")
    result = subprocess.run("mysql --version", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print(f"✅ MySQL متاح: {result.stdout.strip()}")
        return True
    else:
        print("❌ MySQL غير مثبت أو غير متاح في PATH")
        print("يرجى تثبيت MySQL أولاً من: https://dev.mysql.com/downloads/mysql/")
        return False

def create_database():
    """إنشاء قاعدة البيانات"""
    print("\n📊 إعداد قاعدة البيانات...")
    
    # طلب كلمة مرور MySQL
    mysql_password = getpass.getpass("أدخل كلمة مرور MySQL (root): ")
    
    # إنشاء ملف SQL مؤقت
    sql_content = """
CREATE DATABASE IF NOT EXISTS mauribac_results 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE mauribac_results;
SELECT 'تم إنشاء قاعدة البيانات mauribac_results بنجاح!' AS message;
"""
    
    with open('temp_create_db.sql', 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    # تشغيل أمر MySQL
    if mysql_password:
        command = f'mysql -u root -p{mysql_password} < temp_create_db.sql'
    else:
        command = 'mysql -u root < temp_create_db.sql'
    
    success = run_command(command, "إنشاء قاعدة البيانات", check=False)
    
    # حذف الملف المؤقت
    if os.path.exists('temp_create_db.sql'):
        os.remove('temp_create_db.sql')
    
    return success

def update_settings():
    """تحديث إعدادات Django"""
    print("\n⚙️ تحديث إعدادات Django...")
    
    mysql_password = getpass.getpass("أدخل كلمة مرور MySQL للإعدادات (اتركها فارغة إذا لم تكن هناك كلمة مرور): ")
    
    # قراءة ملف الإعدادات
    with open('mauribac_results/settings.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # تحديث كلمة المرور
    old_password_line = "'PASSWORD': '',"
    new_password_line = f"'PASSWORD': '{mysql_password}',"
    
    content = content.replace(old_password_line, new_password_line)
    
    # كتابة الملف المحدث
    with open('mauribac_results/settings.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم تحديث إعدادات قاعدة البيانات")

def main():
    print("🚀 إعداد MySQL لموقع موريباك...")
    
    # التحقق من MySQL
    if not check_mysql():
        return False
    
    # إنشاء قاعدة البيانات
    if not create_database():
        print("❌ فشل في إنشاء قاعدة البيانات")
        print("تأكد من:")
        print("1. تشغيل خدمة MySQL")
        print("2. صحة كلمة مرور root")
        print("3. صلاحيات إنشاء قواعد البيانات")
        return False
    
    # تحديث الإعدادات
    update_settings()
    
    # تثبيت المتطلبات
    if not run_command("pip install mysqlclient", "تثبيت mysqlclient"):
        print("⚠️ تحذير: فشل في تثبيت mysqlclient")
        print("قد تحتاج لتثبيته يدوياً:")
        print("pip install mysqlclient")
    
    # إنشاء الهجرات
    if not run_command("python manage.py makemigrations", "إنشاء الهجرات"):
        return False
    
    # تطبيق الهجرات
    if not run_command("python manage.py migrate", "تطبيق الهجرات"):
        return False
    
    # إنشاء البيانات التجريبية
    if run_command("python manage.py create_sample_data", "إنشاء البيانات التجريبية"):
        print("✅ تم إنشاء البيانات التجريبية بنجاح")
    else:
        print("⚠️ تحذير: فشل في إنشاء البيانات التجريبية")
    
    print("\n🎉 تم إعداد MySQL بنجاح!")
    print("📍 يمكنك الآن تشغيل الموقع باستخدام:")
    print("python manage.py runserver")
    print("\n🌐 الموقع سيكون متاحاً على: http://127.0.0.1:8000/")
    
    # سؤال عن تشغيل الخادم
    start_server = input("\nهل تريد تشغيل الخادم الآن؟ (y/n): ")
    if start_server.lower() in ['y', 'yes', 'نعم']:
        print("\n🌐 تشغيل خادم Django...")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        try:
            subprocess.run("python manage.py runserver", shell=True, check=True)
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف الخادم بنجاح")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
