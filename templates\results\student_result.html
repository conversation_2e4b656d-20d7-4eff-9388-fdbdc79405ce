{% extends 'base.html' %}

{% block title %}{{ result.student.full_name }} - {{ competition.name }} - موريباك{% endblock %}

{% block content %}
<!-- Header -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-6 mb-3">نتيجة الطالب</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                    <a href="{% url 'home' %}" class="text-white">الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'competition_detail' competition.slug %}" class="text-white">{{ competition.name }}</a>
                </li>
                <li class="breadcrumb-item active text-white" aria-current="page">{{ result.student.full_name }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Student Result -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card result-card">
                    <div class="card-header bg-success text-white text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-user-graduate me-2"></i>
                            {{ result.student.full_name }}
                        </h3>
                    </div>
                    
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">معلومات الطالب</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>رقم الطالب:</strong></td>
                                        <td>{{ result.student_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الاسم الكامل:</strong></td>
                                        <td>{{ result.student.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الجنس:</strong></td>
                                        <td>{{ result.student.get_gender_display }}</td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">معلومات المسابقة</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>المسابقة:</strong></td>
                                        <td>{{ competition.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الشعبة:</strong></td>
                                        <td>
                                            <a href="{% url 'subject_results' competition.slug result.subject.code %}" 
                                               class="text-decoration-none">
                                                {{ result.subject.name }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>المدرسة:</strong></td>
                                        <td>
                                            <a href="{% url 'school_results' competition.slug result.subject.code result.school.name|urlencode %}" 
                                               class="text-decoration-none">
                                                {{ result.school.name }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>الولاية:</strong></td>
                                        <td>
                                            <a href="{% url 'wilaya_results' competition.slug result.subject.code result.school.wilaya.name|urlencode %}" 
                                               class="text-decoration-none">
                                                {{ result.school.wilaya.name }}
                                            </a>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Results -->
                        <div class="text-center">
                            <h4 class="text-primary mb-4">النتيجة</h4>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h2 class="card-title">{{ result.average }}</h2>
                                            <p class="card-text">المعدل</p>
                                        </div>
                                    </div>
                                </div>
                                
                                {% if result.rank %}
                                <div class="col-md-4">
                                    <div class="card bg-warning text-dark">
                                        <div class="card-body">
                                            <h2 class="card-title">{{ result.rank }}</h2>
                                            <p class="card-text">الترتيب العام</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if result.rank_in_subject %}
                                <div class="col-md-4">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h2 class="card-title">{{ result.rank_in_subject }}</h2>
                                            <p class="card-text">الترتيب في الشعبة</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mt-4">
                                <span class="badge bg-{{ result.status|yesno:'success,danger,secondary' }} fs-6">
                                    {{ result.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="text-center mt-4">
                    <a href="{% url 'competition_detail' competition.slug %}" class="btn btn-primary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة إلى المسابقة
                    </a>
                    <a href="{% url 'subject_results' competition.slug result.subject.code %}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>
                        عرض نتائج الشعبة
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
/* تحسين تصميم صفحة النتيجة */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
}

.result-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none;
    padding: 2rem;
}

.card-header h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.card-body {
    padding: 2rem;
}

/* تحسين الجداول */
.table td {
    padding: 1rem 0.5rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.table td:first-child {
    width: 40%;
    color: #6c757d;
    font-weight: 600;
}

.table td:last-child {
    color: #495057;
    font-weight: 500;
}

/* تحسين بطاقات النتائج */
.card.bg-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
    transition: transform 0.3s ease;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%) !important;
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(255, 234, 167, 0.3);
    transition: transform 0.3s ease;
}

.card.bg-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.3);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-body {
    padding: 2rem 1rem;
}

.card-title {
    font-size: 3rem !important;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card-text {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* تحسين شارة الحالة */
.badge {
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.2rem !important;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
}

/* تحسين الأزرار */
.btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* تحسين الروابط */
a.text-decoration-none {
    color: #007bff;
    font-weight: 500;
    transition: color 0.3s ease;
}

a.text-decoration-none:hover {
    color: #0056b3;
    text-decoration: underline !important;
}

/* تحسين breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: white;
    font-weight: 600;
}

/* تأثيرات إضافية */
.text-primary {
    color: #667eea !important;
}

hr {
    border-top: 2px solid #e9ecef;
    margin: 2rem 0;
}

/* تحسين للطباعة */
@media print {
    .hero-section {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .btn {
        display: none;
    }

    .breadcrumb {
        display: none;
    }
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
    }

    .card-header h3 {
        font-size: 1.5rem;
    }

    .card-title {
        font-size: 2rem !important;
    }

    .badge {
        padding: 0.75rem 1.5rem;
        font-size: 1rem !important;
    }
}
</style>
{% endblock %}
