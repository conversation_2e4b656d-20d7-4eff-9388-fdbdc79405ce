{% extends 'base.html' %}

{% block title %}{{ result.student.full_name }} - {{ competition.name }} - موريباك{% endblock %}

{% block content %}
<!-- Header -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-6 mb-3">نتيجة الطالب</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                    <a href="{% url 'home' %}" class="text-white">الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'competition_detail' competition.slug %}" class="text-white">{{ competition.name }}</a>
                </li>
                <li class="breadcrumb-item active text-white" aria-current="page">{{ result.student.full_name }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Student Result -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card result-card">
                    <div class="card-header bg-success text-white text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-user-graduate me-2"></i>
                            {{ result.student.full_name }}
                        </h3>
                    </div>
                    
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">معلومات الطالب</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>رقم الطالب:</strong></td>
                                        <td>{{ result.student_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الاسم الكامل:</strong></td>
                                        <td>{{ result.student.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الجنس:</strong></td>
                                        <td>{{ result.student.get_gender_display }}</td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">معلومات المسابقة</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>المسابقة:</strong></td>
                                        <td>{{ competition.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الشعبة:</strong></td>
                                        <td>
                                            <a href="{% url 'subject_results' competition.slug result.subject.code %}" 
                                               class="text-decoration-none">
                                                {{ result.subject.name }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>المدرسة:</strong></td>
                                        <td>
                                            <a href="{% url 'school_results' competition.slug result.subject.code result.school.name|urlencode %}" 
                                               class="text-decoration-none">
                                                {{ result.school.name }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>الولاية:</strong></td>
                                        <td>
                                            <a href="{% url 'wilaya_results' competition.slug result.subject.code result.school.wilaya.name|urlencode %}" 
                                               class="text-decoration-none">
                                                {{ result.school.wilaya.name }}
                                            </a>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Results -->
                        <div class="text-center">
                            <h4 class="text-primary mb-4">النتيجة</h4>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h2 class="card-title">{{ result.average }}</h2>
                                            <p class="card-text">المعدل</p>
                                        </div>
                                    </div>
                                </div>
                                
                                {% if result.rank %}
                                <div class="col-md-4">
                                    <div class="card bg-warning text-dark">
                                        <div class="card-body">
                                            <h2 class="card-title">{{ result.rank }}</h2>
                                            <p class="card-text">الترتيب العام</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if result.rank_in_subject %}
                                <div class="col-md-4">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h2 class="card-title">{{ result.rank_in_subject }}</h2>
                                            <p class="card-text">الترتيب في الشعبة</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mt-4">
                                <span class="badge bg-{{ result.status|yesno:'success,danger,secondary' }} fs-6">
                                    {{ result.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="text-center mt-4">
                    <div class="row justify-content-center">
                        <div class="col-md-10">
                            <div class="btn-group-custom">
                                <a href="{% url 'competition_detail' competition.slug %}" class="btn btn-primary me-2 mb-2">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    العودة إلى المسابقة
                                </a>
                                <a href="{% url 'subject_results' competition.slug result.subject.code %}" class="btn btn-outline-primary me-2 mb-2">
                                    <i class="fas fa-list me-2"></i>
                                    عرض نتائج الشعبة
                                </a>
                                <button onclick="window.print()" class="btn btn-success me-2 mb-2">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة النتيجة
                                </button>
                                <button onclick="shareResult()" class="btn btn-info me-2 mb-2">
                                    <i class="fas fa-share-alt me-2"></i>
                                    مشاركة النتيجة
                                </button>
                                <button onclick="downloadResult()" class="btn btn-warning mb-2">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل PDF
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات إضافية -->
                <div class="mt-5">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="stats-card">
                                <h5 class="stats-title">📊 إحصائيات الشعبة</h5>
                                <div class="stats-item">
                                    <span class="stats-label">متوسط الشعبة:</span>
                                    <span class="stats-value">15.25</span>
                                </div>
                                <div class="stats-item">
                                    <span class="stats-label">أعلى معدل في الشعبة:</span>
                                    <span class="stats-value">18.75</span>
                                </div>
                                <div class="stats-item">
                                    <span class="stats-label">عدد الناجحين:</span>
                                    <span class="stats-value">85%</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="stats-card">
                                <h5 class="stats-title">🏫 إحصائيات المدرسة</h5>
                                <div class="stats-item">
                                    <span class="stats-label">متوسط المدرسة:</span>
                                    <span class="stats-value">16.10</span>
                                </div>
                                <div class="stats-item">
                                    <span class="stats-label">ترتيب المدرسة:</span>
                                    <span class="stats-value">الثالثة</span>
                                </div>
                                <div class="stats-item">
                                    <span class="stats-label">عدد طلاب المدرسة:</span>
                                    <span class="stats-value">45 طالب</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
/* تحسين تصميم صفحة النتيجة */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
}

.result-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none;
    padding: 2rem;
}

.card-header h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.card-body {
    padding: 2rem;
}

/* تحسين الجداول */
.table td {
    padding: 1rem 0.5rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.table td:first-child {
    width: 40%;
    color: #6c757d;
    font-weight: 600;
}

.table td:last-child {
    color: #495057;
    font-weight: 500;
}

/* تحسين بطاقات النتائج */
.card.bg-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
    transition: transform 0.3s ease;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%) !important;
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(255, 234, 167, 0.3);
    transition: transform 0.3s ease;
}

.card.bg-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.3);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-body {
    padding: 2rem 1rem;
}

.card-title {
    font-size: 3rem !important;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card-text {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* تحسين شارة الحالة */
.badge {
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.2rem !important;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
}

/* تحسين الأزرار */
.btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* تحسين الروابط */
a.text-decoration-none {
    color: #007bff;
    font-weight: 500;
    transition: color 0.3s ease;
}

a.text-decoration-none:hover {
    color: #0056b3;
    text-decoration: underline !important;
}

/* تحسين breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: white;
    font-weight: 600;
}

/* تأثيرات إضافية */
.text-primary {
    color: #667eea !important;
}

hr {
    border-top: 2px solid #e9ecef;
    margin: 2rem 0;
}

/* تحسين للطباعة */
@media print {
    .hero-section {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .btn {
        display: none;
    }

    .breadcrumb {
        display: none;
    }
}

/* تحسين الأزرار المجمعة */
.btn-group-custom {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
}

.btn-group-custom .btn {
    flex: 0 0 auto;
    min-width: 150px;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 15px;
    padding: 1.5rem;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-title {
    color: #495057;
    font-weight: bold;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007bff;
}

.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.stats-item:last-child {
    border-bottom: none;
}

.stats-label {
    color: #6c757d;
    font-weight: 500;
}

.stats-value {
    color: #495057;
    font-weight: bold;
    background: #007bff;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

/* تحسين أزرار الإجراءات */
.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
    color: #212529;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

/* تأثيرات إضافية للأزرار */
.btn:active {
    transform: translateY(1px);
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
    }

    .card-header h3 {
        font-size: 1.5rem;
    }

    .card-title {
        font-size: 2rem !important;
    }

    .badge {
        padding: 0.75rem 1.5rem;
        font-size: 1rem !important;
    }

    .btn-group-custom {
        flex-direction: column;
        align-items: center;
    }

    .btn-group-custom .btn {
        width: 100%;
        max-width: 250px;
        margin-bottom: 0.5rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}
</style>

<script>
// مشاركة النتيجة
function shareResult() {
    const studentName = "{{ result.student.full_name }}";
    const studentNumber = "{{ result.student_number }}";
    const average = "{{ result.average }}";
    const competition = "{{ competition.name }}";
    const currentUrl = window.location.href;

    const shareText = `🎓 نتيجة ${studentName}\n📋 رقم الطالب: ${studentNumber}\n📊 المعدل: ${average}\n🏆 ${competition}\n\n${currentUrl}`;

    if (navigator.share) {
        // استخدام Web Share API إذا كان متاحاً
        navigator.share({
            title: `نتيجة ${studentName}`,
            text: shareText,
            url: currentUrl
        }).then(() => {
            showNotification('تم مشاركة النتيجة بنجاح!', 'success');
        }).catch((error) => {
            console.log('خطأ في المشاركة:', error);
            fallbackShare(shareText);
        });
    } else {
        fallbackShare(shareText);
    }
}

// مشاركة احتياطية
function fallbackShare(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ رابط النتيجة! يمكنك مشاركته الآن.', 'success');
        }).catch(() => {
            showShareModal(text);
        });
    } else {
        showShareModal(text);
    }
}

// عرض نافذة المشاركة
function showShareModal(text) {
    const modal = document.createElement('div');
    modal.className = 'share-modal';
    modal.innerHTML = `
        <div class="share-modal-content">
            <div class="share-modal-header">
                <h5>مشاركة النتيجة</h5>
                <button onclick="closeShareModal()" class="close-btn">&times;</button>
            </div>
            <div class="share-modal-body">
                <textarea id="shareText" readonly>${text}</textarea>
                <div class="share-buttons">
                    <button onclick="copyShareText()" class="btn btn-primary">
                        <i class="fas fa-copy me-2"></i>نسخ النص
                    </button>
                    <a href="https://wa.me/?text=${encodeURIComponent(text)}" target="_blank" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>واتساب
                    </a>
                    <a href="https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(text)}" target="_blank" class="btn btn-info">
                        <i class="fab fa-telegram me-2"></i>تيليجرام
                    </a>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إضافة CSS للنافذة
    const style = document.createElement('style');
    style.textContent = `
        .share-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .share-modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
        }
        .share-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .share-modal-body {
            padding: 1.5rem;
        }
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
        #shareText {
            width: 100%;
            height: 150px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            resize: none;
        }
        .share-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        .share-buttons .btn {
            flex: 1;
            min-width: 120px;
        }
    `;
    document.head.appendChild(style);
}

// نسخ نص المشاركة
function copyShareText() {
    const textarea = document.getElementById('shareText');
    textarea.select();
    document.execCommand('copy');
    showNotification('تم نسخ النص!', 'success');
    closeShareModal();
}

// إغلاق نافذة المشاركة
function closeShareModal() {
    const modal = document.querySelector('.share-modal');
    if (modal) {
        modal.remove();
    }
}

// تحميل النتيجة كـ PDF
function downloadResult() {
    showNotification('جاري تحضير ملف PDF...', 'info');

    // إخفاء الأزرار مؤقتاً للطباعة
    const buttons = document.querySelector('.btn-group-custom');
    const originalDisplay = buttons.style.display;
    buttons.style.display = 'none';

    // استخدام html2pdf إذا كان متاحاً
    if (typeof html2pdf !== 'undefined') {
        const element = document.querySelector('.container');
        const opt = {
            margin: 1,
            filename: `نتيجة_{{ result.student_number }}_{{ result.student.first_name }}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
        };

        html2pdf().set(opt).from(element).save().then(() => {
            buttons.style.display = originalDisplay;
            showNotification('تم تحميل ملف PDF بنجاح!', 'success');
        });
    } else {
        // استخدام الطباعة كبديل
        setTimeout(() => {
            window.print();
            buttons.style.display = originalDisplay;
            showNotification('يمكنك حفظ الصفحة كـ PDF من خيارات الطباعة', 'info');
        }, 500);
    }
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
    `;

    // إضافة CSS للإشعار
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease;
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 5 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// إضافة CSS للحركات
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(animationStyle);

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.card, .stats-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // إضافة تأثيرات للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
