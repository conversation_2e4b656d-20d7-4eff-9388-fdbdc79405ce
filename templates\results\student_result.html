{% extends 'base.html' %}

{% block title %}{{ result.student.full_name }} - {{ competition.name }} - موريباك{% endblock %}

{% block content %}
<!-- Header -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-6 mb-3">نتيجة الطالب</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                    <a href="{% url 'home' %}" class="text-white">الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'competition_detail' competition.slug %}" class="text-white">{{ competition.name }}</a>
                </li>
                <li class="breadcrumb-item active text-white" aria-current="page">{{ result.student.full_name }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Student Result -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header Card -->
                <div class="card mb-4">
                    <div class="card-header text-center text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <h3 class="mb-0">نتيجة الطالب</h3>
                    </div>
                </div>

                <!-- Student Info Card -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">معلومات الطالب</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>الرقم:</strong></td>
                                        <td>{{ result.student_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الاسم:</strong></td>
                                        <td>{{ result.student.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الجنس:</strong></td>
                                        <td>{{ result.student.get_gender_display }}</td>
                                    </tr>
                                </table>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">معلومات المسابقة</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>المسابقة:</strong></td>
                                        <td>{{ competition.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الشعبة:</strong></td>
                                        <td>{{ result.subject.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المدرسة:</strong></td>
                                        <td>{{ result.school.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الولاية:</strong></td>
                                        <td>{{ result.school.wilaya.name }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Result Card -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h6 class="text-center mb-4" style="color: #667eea;">النتيجة</h6>

                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <div class="card border-0" style="background: #f8f9fa;">
                                    <div class="card-body py-4">
                                        <h2 class="text-success mb-2">{{ result.rank_in_subject|default:"2" }}</h2>
                                        <small class="text-muted">الترتيب في الشعبة</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <div class="card border-0" style="background: #f8f9fa;">
                                    <div class="card-body py-4">
                                        <h2 class="text-primary mb-2">{{ result.average }}</h2>
                                        <small class="text-muted">المعدل</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <div class="card border-0" style="background: #f8f9fa;">
                                    <div class="card-body py-4">
                                        <h2 class="text-warning mb-2">{{ result.rank|default:"2" }}</h2>
                                        <small class="text-muted">الترتيب العام</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <span class="badge bg-success px-4 py-2 fs-6">
                                <i class="fas fa-check-circle me-2"></i>
                                ناجح
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="text-center mt-4">
                    <div class="row justify-content-center">
                        <div class="col-md-10">
                            <div class="d-flex flex-wrap justify-content-center gap-2">
                                <button onclick="window.print()" class="btn btn-success px-4 py-2">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة
                                </button>
                                <a href="{% url 'competition_detail' competition.slug %}" class="btn btn-primary px-4 py-2">
                                    <i class="fas fa-search me-2"></i>
                                    بحث جديد
                                </a>
                                <a href="{% url 'subject_results' competition.slug result.subject.code %}" class="btn btn-success px-4 py-2">
                                    <i class="fas fa-list me-2"></i>
                                    قائمة النتائج
                                </a>
                                <button onclick="shareResult()" class="btn btn-primary px-4 py-2">
                                    <i class="fas fa-share-alt me-2"></i>
                                    عرض في صفحة منفصلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
/* تحسين تصميم صفحة النتيجة */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
}

.card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.card-header {
    border: none;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* تحسين الجداول */
.table td {
    padding: 0.75rem 0.5rem;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
}

.table td:first-child {
    width: 35%;
    color: #6c757d;
    font-weight: 600;
}

.table td:last-child {
    color: #495057;
    font-weight: 500;
}

/* تحسين الأزرار */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.gap-2 {
    gap: 0.5rem !important;
}

/* تحسين شارة الحالة */
.badge {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem !important;
    font-weight: 600;
}

/* تحسين breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: white;
    font-weight: 600;
}

/* تحسين للطباعة */
@media print {
    .btn {
        display: none;
    }
    .breadcrumb {
        display: none;
    }
}
</style>

<script>
// مشاركة النتيجة
function shareResult() {
    const studentName = "{{ result.student.full_name }}";
    const studentNumber = "{{ result.student_number }}";
    const average = "{{ result.average }}";
    const competition = "{{ competition.name }}";
    const currentUrl = window.location.href;

    const shareText = `🎓 نتيجة ${studentName}\n📋 رقم الطالب: ${studentNumber}\n📊 المعدل: ${average}\n🏆 ${competition}\n\n${currentUrl}`;

    if (navigator.share) {
        // استخدام Web Share API إذا كان متاحاً
        navigator.share({
            title: `نتيجة ${studentName}`,
            text: shareText,
            url: currentUrl
        }).then(() => {
            showNotification('تم مشاركة النتيجة بنجاح!', 'success');
        }).catch((error) => {
            console.log('خطأ في المشاركة:', error);
            fallbackShare(shareText);
        });
    } else {
        fallbackShare(shareText);
    }
}

// مشاركة احتياطية
function fallbackShare(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ رابط النتيجة! يمكنك مشاركته الآن.', 'success');
        }).catch(() => {
            showShareModal(text);
        });
    } else {
        showShareModal(text);
    }
}

// عرض نافذة المشاركة
function showShareModal(text) {
    const modal = document.createElement('div');
    modal.className = 'share-modal';
    modal.innerHTML = `
        <div class="share-modal-content">
            <div class="share-modal-header">
                <h5>مشاركة النتيجة</h5>
                <button onclick="closeShareModal()" class="close-btn">&times;</button>
            </div>
            <div class="share-modal-body">
                <textarea id="shareText" readonly>${text}</textarea>
                <div class="share-buttons">
                    <button onclick="copyShareText()" class="btn btn-primary">
                        <i class="fas fa-copy me-2"></i>نسخ النص
                    </button>
                    <a href="https://wa.me/?text=${encodeURIComponent(text)}" target="_blank" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>واتساب
                    </a>
                    <a href="https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(text)}" target="_blank" class="btn btn-info">
                        <i class="fab fa-telegram me-2"></i>تيليجرام
                    </a>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إضافة CSS للنافذة
    const style = document.createElement('style');
    style.textContent = `
        .share-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .share-modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
        }
        .share-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .share-modal-body {
            padding: 1.5rem;
        }
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
        #shareText {
            width: 100%;
            height: 150px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            resize: none;
        }
        .share-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        .share-buttons .btn {
            flex: 1;
            min-width: 120px;
        }
    `;
    document.head.appendChild(style);
}

// نسخ نص المشاركة
function copyShareText() {
    const textarea = document.getElementById('shareText');
    textarea.select();
    document.execCommand('copy');
    showNotification('تم نسخ النص!', 'success');
    closeShareModal();
}

// إغلاق نافذة المشاركة
function closeShareModal() {
    const modal = document.querySelector('.share-modal');
    if (modal) {
        modal.remove();
    }
}

// تحميل النتيجة كـ PDF
function downloadResult() {
    showNotification('جاري تحضير ملف PDF...', 'info');

    // إخفاء الأزرار مؤقتاً للطباعة
    const buttons = document.querySelector('.btn-group-custom');
    const originalDisplay = buttons.style.display;
    buttons.style.display = 'none';

    // استخدام html2pdf إذا كان متاحاً
    if (typeof html2pdf !== 'undefined') {
        const element = document.querySelector('.container');
        const opt = {
            margin: 1,
            filename: `نتيجة_{{ result.student_number }}_{{ result.student.first_name }}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
        };

        html2pdf().set(opt).from(element).save().then(() => {
            buttons.style.display = originalDisplay;
            showNotification('تم تحميل ملف PDF بنجاح!', 'success');
        });
    } else {
        // استخدام الطباعة كبديل
        setTimeout(() => {
            window.print();
            buttons.style.display = originalDisplay;
            showNotification('يمكنك حفظ الصفحة كـ PDF من خيارات الطباعة', 'info');
        }, 500);
    }
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
    `;

    // إضافة CSS للإشعار
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease;
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 5 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// إضافة CSS للحركات
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(animationStyle);

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.card, .stats-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // إضافة تأثيرات للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
