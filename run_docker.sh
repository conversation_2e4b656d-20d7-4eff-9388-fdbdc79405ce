#!/bin/bash

echo "🐳 بدء تشغيل موقع موريباك باستخدام Docker..."

# التحقق من وجود Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً."
    exit 1
fi

# إيقاف الحاويات السابقة إن وجدت
echo "🛑 إيقاف الحاويات السابقة..."
docker-compose down

# بناء وتشغيل الحاويات
echo "🔨 بناء الحاويات..."
docker-compose build

echo "🚀 تشغيل الحاويات..."
docker-compose up -d

# انتظار تشغيل قاعدة البيانات
echo "⏳ انتظار تشغيل قاعدة البيانات..."
sleep 30

# تطبيق الهجرات
echo "🔄 تطبيق هجرات قاعدة البيانات..."
docker-compose exec web python manage.py migrate --settings=mauribac_results.settings_production

# إنشاء البيانات التجريبية
echo "📊 إنشاء البيانات التجريبية..."
docker-compose exec web python manage.py create_sample_data --settings=mauribac_results.settings_production

echo "✅ تم تشغيل الموقع بنجاح!"
echo "🌐 الموقع متاح على: http://localhost"
echo "🔧 لوحة الإدارة: http://localhost/admin/"
echo ""
echo "📋 أوامر مفيدة:"
echo "  - عرض السجلات: docker-compose logs -f"
echo "  - إيقاف الموقع: docker-compose down"
echo "  - إعادة التشغيل: docker-compose restart"
echo "  - الدخول للحاوية: docker-compose exec web bash"
