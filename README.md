# 🎓 موقع موريباك - نتائج المسابقات الوطنية

موقع ويب متكامل لعرض وفرز نتائج المسابقات الوطنية مشابه لموقع mauribac.com، مطور باستخدام Django و MySQL مع واجهة عربية جميلة ومتجاوبة.

## ✨ المميزات

- 🔍 **البحث السريع** برقم الطالب مع نتائج فورية
- 📊 **عرض الترتيب والأوائل** لكل شعبة مع أيقونات الميداليات
- 🏫 **تصفية متقدمة** حسب الشعبة والولاية والمدرسة
- 🌐 **واجهة عربية كاملة** مع دعم RTL
- 📱 **تصميم متجاوب** يعمل على جميع الأجهزة
- 🎨 **تصميم جذاب** باستخدام Bootstrap 5
- 🔒 **أمان محسن** مع حماية شاملة
- 🐳 **دعم Docker** للنشر السهل

## 🛠️ متطلبات التشغيل

- Python 3.8+
- MySQL 5.7+ أو MariaDB 10.2+ (للإنتاج)
- SQLite (للاختبار السريع)
- Docker (اختياري للنشر)

## 🚀 طرق التشغيل

### 1️⃣ التشغيل السريع (مستحسن للاختبار)

#### على Windows:
```cmd
run_server.bat
```

#### على Linux/Mac:
```bash
python run_server.py
```

### 2️⃣ التشغيل باستخدام Docker (مستحسن للإنتاج)

#### على Windows:
```cmd
run_docker.bat
```

#### على Linux/Mac:
```bash
chmod +x run_docker.sh
./run_docker.sh
```

### 3️⃣ التشغيل اليدوي

## التثبيت والتشغيل اليدوي

### 🚀 التشغيل السريع (مستحسن للاختبار)

#### على Windows:
```cmd
run_server.bat
```

#### على Linux/Mac:
```bash
python run_server.py
```

أو يدوياً:
```bash
pip install django
python manage.py makemigrations results --settings=mauribac_results.settings_sqlite
python manage.py migrate --settings=mauribac_results.settings_sqlite
python manage.py create_sample_data --settings=mauribac_results.settings_sqlite
python manage.py runserver --settings=mauribac_results.settings_sqlite
```

الموقع سيكون متاحاً على: http://127.0.0.1:8000/

### 🗄️ للإنتاج مع MySQL

#### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

#### 2. إعداد قاعدة البيانات

```sql
mysql -u root -p < setup_database.sql
```

#### 3. تحديث إعدادات قاعدة البيانات

قم بتحديث ملف `mauribac_results/settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'mauribac_results',
        'USER': 'your_mysql_username',
        'PASSWORD': 'your_mysql_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
```

#### 4. تطبيق الهجرات

```bash
python manage.py makemigrations
python manage.py migrate
python manage.py create_sample_data
python manage.py createsuperuser
python manage.py runserver
```

## بنية المشروع

```
mauribac_results/
├── mauribac_results/          # إعدادات المشروع الرئيسية
├── results/                   # تطبيق النتائج
│   ├── models.py             # نماذج قاعدة البيانات
│   ├── views.py              # عرض البيانات
│   ├── urls.py               # توجيه الروابط
│   ├── admin.py              # لوحة الإدارة
│   └── management/           # أوامر إدارية مخصصة
├── templates/                # قوالب HTML
│   ├── base.html            # القالب الأساسي
│   └── results/             # قوالب النتائج
├── static/                   # ملفات CSS/JS/Images
├── requirements.txt          # متطلبات Python
└── README.md                # هذا الملف
```

## النماذج الرئيسية

- **Wilaya**: الولايات
- **School**: المدارس
- **Subject**: الشعب الدراسية
- **Competition**: المسابقات
- **Student**: الطلاب
- **Result**: النتائج

## الصفحات المتاحة

- `/` - الصفحة الرئيسية (قائمة المسابقات)
- `/<competition-slug>/` - صفحة المسابقة مع البحث
- `/<competition-slug>/<subject-code>/` - نتائج شعبة محددة
- `/<competition-slug>/numero/<student-number>/` - نتيجة طالب محدد
- `/<competition-slug>/<subject-code>/ecole/<school-name>/` - نتائج مدرسة محددة
- `/<competition-slug>/<subject-code>/wilaya/<wilaya-name>/` - نتائج ولاية محددة

## 🧪 تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
python run_tests.py

# تشغيل اختبارات Django فقط
python manage.py test --settings=mauribac_results.settings_sqlite

# فحص جودة الكود
python manage.py check --settings=mauribac_results.settings_sqlite
```

## 📁 الملفات المهمة

### ملفات التشغيل:
- `run_server.bat` / `run_server.py` - تشغيل سريع
- `run_docker.bat` / `run_docker.sh` - تشغيل Docker
- `run_tests.py` - تشغيل الاختبارات

### ملفات الإعدادات:
- `mauribac_results/settings.py` - إعدادات MySQL
- `mauribac_results/settings_sqlite.py` - إعدادات SQLite
- `mauribac_results/settings_production.py` - إعدادات الإنتاج

### ملفات Docker:
- `Dockerfile` - صورة Docker
- `docker-compose.yml` - تكوين الخدمات
- `nginx.conf` - إعدادات Nginx

### ملفات التوثيق:
- `USER_GUIDE.md` - دليل المستخدم
- `PROJECT_SUMMARY.md` - ملخص المشروع
- `DEPLOYMENT_GUIDE.md` - دليل النشر

## 📊 إضافة بيانات جديدة

يمكن إضافة البيانات من خلال:

1. **لوحة الإدارة Django**: `/admin/`
2. **استيراد البيانات برمجياً**
3. **إنشاء أوامر إدارية مخصصة**
4. **ملفات CSV/Excel** (يمكن تطويرها)

## التطوير والتخصيص

لتخصيص الموقع:

1. قم بتعديل القوالب في مجلد `templates/`
2. أضف ملفات CSS مخصصة في مجلد `static/`
3. عدل النماذج في `results/models.py`
4. أضف وظائف جديدة في `results/views.py`

## الدعم والمساهمة

هذا المشروع مفتوح المصدر ويمكن تطويره وتحسينه.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
