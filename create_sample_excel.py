#!/usr/bin/env python
"""
إنشاء ملف Excel تجريبي للاختبار
"""
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment

def create_sample_excel():
    """إنشاء ملف Excel تجريبي"""
    
    # إنشاء ملف Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "نتائج تجريبية"
    
    # إعداد الرؤوس
    headers = [
        'رقم_الطالب', 'الاسم_الأول', 'اسم_العائلة', 'الجنس',
        'الشعبة', 'المدرسة', 'الولاية', 'المعدل', 'الحالة'
    ]
    
    # كتابة الرؤوس
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")
    
    # بيانات تجريبية
    sample_data = [
        ['20001', 'أحمد', 'محمد', 'M', 'العلوم الطبيعية', 'ثانوية النجاح', 'انواكشوط 1 (الشمالية)', 17.25, 'PASS'],
        ['20002', 'فاطمة', 'علي', 'F', 'الرياضيات', 'ثانوية التفوق', 'انواكشوط 2 (الغربية)', 16.80, 'PASS'],
        ['20003', 'محمد', 'أحمد', 'M', 'الآداب الأصلية', 'ثانوية الأمل', 'انواكشوط 3 (الجنوبية)', 15.50, 'PASS'],
        ['20004', 'عائشة', 'محمد', 'F', 'الآداب العصرية', 'ثانوية المستقبل', 'داخلت انواذيبو', 14.75, 'PASS'],
        ['20005', 'عبد الله', 'إبراهيم', 'M', 'التقنية', 'المعهد التقني', 'تيرس زمور', 13.20, 'PASS'],
        ['20006', 'مريم', 'عمر', 'F', 'الهندسة الكهربائية', 'معهد الهندسة', 'آدرار', 16.40, 'PASS'],
        ['20007', 'يوسف', 'حسن', 'M', 'اللغات', 'ثانوية اللغات', 'كوركل', 15.90, 'PASS'],
        ['20008', 'خديجة', 'علي', 'F', 'العلوم الطبيعية', 'ثانوية العلوم', 'لعصابه', 17.60, 'PASS'],
        ['20009', 'إبراهيم', 'محمد', 'M', 'الرياضيات', 'ثانوية الرياضيات', 'البراكنه', 18.10, 'PASS'],
        ['20010', 'آمنة', 'أحمد', 'F', 'الآداب الأصلية', 'ثانوية الآداب', 'الترارزه', 14.30, 'PASS'],
    ]
    
    # كتابة البيانات
    for row_num, row_data in enumerate(sample_data, 2):
        for col_num, value in enumerate(row_data, 1):
            ws.cell(row=row_num, column=col_num, value=value)
    
    # تنسيق الأعمدة
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # حفظ الملف
    filename = 'نتائج_تجريبية_للاستيراد.xlsx'
    wb.save(filename)
    print(f"✅ تم إنشاء ملف Excel التجريبي: {filename}")
    
    return filename

if __name__ == "__main__":
    create_sample_excel()
