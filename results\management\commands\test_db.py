from django.core.management.base import BaseCommand
from results.models import Competition, Subject, Wilaya, School, Student, Result

class Command(BaseCommand):
    help = 'اختبار قاعدة البيانات'

    def handle(self, *args, **options):
        self.stdout.write('اختبار قاعدة البيانات...')
        
        # عرض الإحصائيات
        competitions_count = Competition.objects.count()
        students_count = Student.objects.count()
        results_count = Result.objects.count()
        subjects_count = Subject.objects.count()
        schools_count = School.objects.count()
        wilayas_count = Wilaya.objects.count()
        
        self.stdout.write(f'المسابقات: {competitions_count}')
        self.stdout.write(f'الطلاب: {students_count}')
        self.stdout.write(f'النتائج: {results_count}')
        self.stdout.write(f'الشعب: {subjects_count}')
        self.stdout.write(f'المدارس: {schools_count}')
        self.stdout.write(f'الولايات: {wilayas_count}')
        
        # عرض المسابقات
        self.stdout.write('\nالمسابقات المتاحة:')
        for competition in Competition.objects.all():
            results_in_comp = Result.objects.filter(competition=competition).count()
            self.stdout.write(f'  - {competition.name} ({competition.year}): {results_in_comp} نتيجة')
        
        # اختبار البحث
        if results_count > 0:
            first_result = Result.objects.first()
            self.stdout.write(f'\nمثال على نتيجة:')
            self.stdout.write(f'  الطالب: {first_result.student.first_name} {first_result.student.last_name}')
            self.stdout.write(f'  الرقم: {first_result.student_number}')
            self.stdout.write(f'  المعدل: {first_result.average}')
            self.stdout.write(f'  المسابقة: {first_result.competition.name}')
        
        self.stdout.write(
            self.style.SUCCESS('\nاختبار قاعدة البيانات مكتمل!')
        )
