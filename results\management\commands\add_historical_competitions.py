from django.core.management.base import BaseCommand
from results.models import Competition, Subject, Wilaya, School, Student, Result
from django.utils.text import slugify
import random
from faker import Faker

fake = Faker('ar_SA')

class Command(BaseCommand):
    help = 'إضافة مسابقات تاريخية (سنوات ماضية)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--start-year',
            type=int,
            help='السنة الأولى',
            default=2020
        )
        parser.add_argument(
            '--end-year',
            type=int,
            help='السنة الأخيرة',
            default=2023
        )

    def handle(self, *args, **options):
        start_year = options['start_year']
        end_year = options['end_year']
        
        self.stdout.write(f'إضافة مسابقات تاريخية من {start_year} إلى {end_year}...')
        
        # أنواع المسابقات التاريخية
        historical_competitions = [
            'الباكلوريا',
            'مسابقة ختم الدروس الإعدادية',
            'مسابقة الإمتياز - الثانوية'
        ]
        
        total_added = 0
        
        for year in range(start_year, end_year + 1):
            for comp_type in historical_competitions:
                competition_name = f"{comp_type} {year}"
                
                # تحقق من وجود المسابقة
                if Competition.objects.filter(name=competition_name).exists():
                    self.stdout.write(f'⚠️ المسابقة موجودة بالفعل: {competition_name}')
                    continue
                
                # إنشاء slug
                base_slug = slugify(competition_name, allow_unicode=True)
                slug = f"{base_slug}-{random.randint(1000, 9999)}"
                
                while Competition.objects.filter(slug=slug).exists():
                    slug = f"{base_slug}-{random.randint(1000, 9999)}"
                
                # إنشاء المسابقة
                competition = Competition.objects.create(
                    name=competition_name,
                    year=year,
                    slug=slug,
                    is_active=True
                )
                
                self.stdout.write(f'✅ تم إنشاء المسابقة التاريخية: {competition_name}')
                
                # إضافة بيانات تجريبية
                self.add_historical_results(competition, year)
                total_added += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إضافة {total_added} مسابقة تاريخية بنجاح!')
        )
    
    def add_historical_results(self, competition, year):
        """إضافة نتائج تاريخية للمسابقة"""
        
        subjects = list(Subject.objects.all())
        schools = list(School.objects.all())
        
        if not subjects or not schools:
            return
        
        # تحديد نطاق الأرقام للسنوات التاريخية
        base_num = (year - 2020) * 1000 + 10000
        
        # إنشاء 15 طالب لكل مسابقة تاريخية
        for i in range(1, 16):
            student = Student.objects.create(
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                birth_date=fake.date_of_birth(minimum_age=18, maximum_age=25)
            )
            
            average = round(random.uniform(7.0, 19.0), 2)
            
            Result.objects.create(
                competition=competition,
                student=student,
                subject=random.choice(subjects),
                school=random.choice(schools),
                student_number=f"{base_num + i}",
                average=average,
                rank=i,
                status='PASS' if average >= 10 else 'FAIL'
            )
        
        # إعادة حساب الترتيب
        results = Result.objects.filter(competition=competition).order_by('-average')
        for rank, result in enumerate(results, 1):
            result.rank = rank
            result.save()
        
        self.stdout.write(f'✅ تم إضافة 15 نتيجة تاريخية للمسابقة {competition.name}')
