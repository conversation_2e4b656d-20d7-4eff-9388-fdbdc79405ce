@echo off
chcp 65001 >nul
echo 🚀 إعداد MySQL السريع لموقع موريباك...
echo.

REM التحقق من MySQL
echo 🔍 التحقق من MySQL...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL غير مثبت أو غير متاح
    echo.
    echo 📥 يرجى تثبيت MySQL أولاً من:
    echo https://dev.mysql.com/downloads/mysql/
    echo.
    echo أو استخدم XAMPP/WAMP للحصول على MySQL بسهولة
    pause
    exit /b 1
)
echo ✅ MySQL متاح

REM إنشاء قاعدة البيانات بدون كلمة مرور أولاً
echo.
echo 📊 محاولة إنشاء قاعدة البيانات بدون كلمة مرور...

echo CREATE DATABASE IF NOT EXISTS mauribac_results CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; > temp_db.sql

mysql -u root < temp_db.sql >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم إنشاء قاعدة البيانات بنجاح
    del temp_db.sql
    goto install_requirements
)

REM إذا فشل، اطلب كلمة المرور
echo ⚠️ فشل بدون كلمة مرور، يرجى إدخال كلمة مرور MySQL
echo.
set /p mysql_password=أدخل كلمة مرور MySQL (root): 

if "%mysql_password%"=="" (
    mysql -u root < temp_db.sql
) else (
    mysql -u root -p%mysql_password% < temp_db.sql
)

if errorlevel 1 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo.
    echo 🔧 تأكد من:
    echo - تشغيل خدمة MySQL (يمكن تشغيلها من XAMPP Control Panel)
    echo - صحة كلمة المرور
    echo - صلاحيات إنشاء قواعد البيانات
    del temp_db.sql
    pause
    exit /b 1
)

del temp_db.sql
echo ✅ تم إنشاء قاعدة البيانات بنجاح

REM تحديث إعدادات Django إذا كانت هناك كلمة مرور
if not "%mysql_password%"=="" (
    echo 🔧 تحديث إعدادات Django...
    powershell -Command "(Get-Content mauribac_results\settings.py) -replace \"'PASSWORD': '',  # ضع كلمة مرور MySQL هنا إذا كانت موجودة\", \"'PASSWORD': '%mysql_password%',\" | Set-Content mauribac_results\settings.py"
    echo ✅ تم تحديث كلمة المرور في الإعدادات
)

:install_requirements
REM تثبيت المتطلبات
echo.
echo 📦 تثبيت mysqlclient...
pip install mysqlclient >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت mysqlclient
    echo 💡 جرب: pip install mysqlclient
    echo أو استخدم: conda install mysqlclient
) else (
    echo ✅ تم تثبيت mysqlclient بنجاح
)

REM إنشاء الهجرات
echo.
echo 🔄 إنشاء الهجرات...
python manage.py makemigrations >nul 2>&1
if errorlevel 1 (
    echo ❌ فشل في إنشاء الهجرات
    pause
    exit /b 1
)
echo ✅ تم إنشاء الهجرات

REM تطبيق الهجرات
echo.
echo 🔄 تطبيق الهجرات...
python manage.py migrate >nul 2>&1
if errorlevel 1 (
    echo ❌ فشل في تطبيق الهجرات
    pause
    exit /b 1
)
echo ✅ تم تطبيق الهجرات

REM إنشاء البيانات التجريبية
echo.
echo 📊 إنشاء البيانات التجريبية...
python manage.py create_sample_data >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في إنشاء البيانات التجريبية
) else (
    echo ✅ تم إنشاء البيانات التجريبية بنجاح
)

echo.
echo 🎉 تم إعداد MySQL بنجاح!
echo.
echo 📍 الموقع جاهز للتشغيل:
echo   python manage.py runserver
echo.
echo 🌐 سيكون متاحاً على: http://127.0.0.1:8000/
echo 🔧 لوحة الإدارة: http://127.0.0.1:8000/admin/
echo.

echo 🚀 تشغيل الخادم الآن...
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo.
python manage.py runserver

pause
