#!/usr/bin/env python
"""
إنشاء مسابقة تجريبية للاختبار
"""
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mauribac_results.settings')
django.setup()

from results.models import Competition, Subject, Wilaya, School, Student, Result
from decimal import Decimal

def create_test_competition():
    """إنشاء مسابقة تجريبية للحذف"""
    
    # إنشاء المسابقة
    competition, created = Competition.objects.get_or_create(
        slug='test-competition-for-deletion',
        defaults={
            'name': 'مسابقة تجريبية للحذف',
            'year': 2023,
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ تم إنشاء مسابقة تجريبية: {competition.name}")
        
        # إضافة بعض النتائج التجريبية
        subject, _ = Subject.objects.get_or_create(
            name='اختبار',
            defaults={'code': 'test'}
        )
        
        wilaya, _ = Wilaya.objects.get_or_create(
            name='ولاية تجريبية',
            defaults={'code': 'TEST'}
        )
        
        school, _ = School.objects.get_or_create(
            name='مدرسة تجريبية',
            defaults={'wilaya': wilaya}
        )
        
        # إنشاء 5 طلاب تجريبيين
        for i in range(1, 6):
            student_number = f"TEST{i:03d}"
            
            student, _ = Student.objects.get_or_create(
                student_number=student_number,
                defaults={
                    'first_name': f'طالب{i}',
                    'last_name': 'تجريبي',
                    'gender': 'M'
                }
            )
            
            Result.objects.get_or_create(
                student_number=student_number,
                competition=competition,
                defaults={
                    'student': student,
                    'subject': subject,
                    'school': school,
                    'average': Decimal('15.00'),
                    'status': 'PASS',
                    'rank': i
                }
            )
        
        print(f"✅ تم إضافة 5 نتائج تجريبية للمسابقة")
        print(f"🔗 رابط المسابقة: http://127.0.0.1:8000/{competition.slug}/")
        
    else:
        print(f"✅ المسابقة التجريبية موجودة: {competition.name}")
    
    return competition

if __name__ == "__main__":
    create_test_competition()
