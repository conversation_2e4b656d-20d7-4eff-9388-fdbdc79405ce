from django.db import models
from django.urls import reverse


class Wilaya(models.Model):
    """نموذج الولايات"""
    name = models.CharField(max_length=100, verbose_name="اسم الولاية")
    code = models.CharField(max_length=10, unique=True, verbose_name="رمز الولاية")
    
    class Meta:
        verbose_name = "ولاية"
        verbose_name_plural = "الولايات"
        ordering = ['name']
    
    def __str__(self):
        return self.name


class School(models.Model):
    """نموذج المدارس"""
    name = models.Char<PERSON>ield(max_length=200, verbose_name="اسم المدرسة")
    wilaya = models.ForeignKey(Wilaya, on_delete=models.CASCADE, verbose_name="الولاية")
    
    class Meta:
        verbose_name = "مدرسة"
        verbose_name_plural = "المدارس"
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Subject(models.Model):
    """نموذج الشعب"""
    name = models.CharField(max_length=100, verbose_name="اسم الشعبة")
    code = models.CharField(max_length=10, unique=True, verbose_name="رمز الشعبة")
    
    class Meta:
        verbose_name = "شعبة"
        verbose_name_plural = "الشعب"
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Competition(models.Model):
    """نموذج المسابقات"""
    name = models.CharField(max_length=200, verbose_name="اسم المسابقة")
    year = models.IntegerField(verbose_name="السنة")
    slug = models.SlugField(unique=True, verbose_name="الرابط")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    
    class Meta:
        verbose_name = "مسابقة"
        verbose_name_plural = "المسابقات"
        ordering = ['-year', 'name']
    
    def __str__(self):
        return f"{self.name} {self.year}"
    
    def get_absolute_url(self):
        return reverse('competition_detail', kwargs={'slug': self.slug})


class Student(models.Model):
    """نموذج الطلاب"""
    GENDER_CHOICES = [
        ('M', 'ذكر'),
        ('F', 'أنثى'),
    ]
    
    student_number = models.CharField(max_length=20, verbose_name="رقم الطالب")
    first_name = models.CharField(max_length=100, verbose_name="الاسم الأول")
    last_name = models.CharField(max_length=100, verbose_name="اسم العائلة")
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, verbose_name="الجنس")
    
    class Meta:
        verbose_name = "طالب"
        verbose_name_plural = "الطلاب"
        ordering = ['last_name', 'first_name']
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"


class Result(models.Model):
    """نموذج النتائج"""
    STATUS_CHOICES = [
        ('PASS', 'ناجح'),
        ('SESSION', 'Session'),
        ('FAIL', 'راسب'),
        ('ABSENT', 'غائب'),
    ]
    
    student = models.ForeignKey(Student, on_delete=models.CASCADE, verbose_name="الطالب")
    competition = models.ForeignKey(Competition, on_delete=models.CASCADE, verbose_name="المسابقة")
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, verbose_name="الشعبة")
    school = models.ForeignKey(School, on_delete=models.CASCADE, verbose_name="المدرسة")
    student_number = models.CharField(max_length=20, verbose_name="رقم الطالب")
    average = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, verbose_name="المعدل")
    rank = models.IntegerField(null=True, blank=True, verbose_name="الترتيب")
    rank_in_subject = models.IntegerField(null=True, blank=True, verbose_name="الترتيب في الشعبة")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PASS', verbose_name="الحالة")

    def save(self, *args, **kwargs):
        """حفظ النتيجة مع تحديد الحالة تلقائياً بناءً على المعدل"""
        if self.average is not None:
            self.status = self.get_status_from_average(self.average)
        super().save(*args, **kwargs)

    @staticmethod
    def get_status_from_average(average):
        """تحديد الحالة بناءً على المعدل"""
        if average is None:
            return 'ABSENT'
        elif average < 8:
            return 'FAIL'
        elif 8 <= average < 10:
            return 'SESSION'
        else:
            return 'PASS'

    def get_status_display_arabic(self):
        """عرض الحالة بالعربية"""
        status_map = {
            'PASS': 'ناجح',
            'SESSION': 'Session',
            'FAIL': 'راسب',
            'ABSENT': 'غائب'
        }
        return status_map.get(self.status, self.status)

    def get_status_color(self):
        """لون الحالة للعرض"""
        color_map = {
            'PASS': 'success',
            'SESSION': 'warning',
            'FAIL': 'danger',
            'ABSENT': 'secondary'
        }
        return color_map.get(self.status, 'secondary')

    class Meta:
        verbose_name = "نتيجة"
        verbose_name_plural = "النتائج"
        unique_together = ['student_number', 'competition']
        ordering = ['-average']
    
    def __str__(self):
        return f"{self.student.full_name} - {self.competition.name} - {self.average}"
    
    def get_absolute_url(self):
        return reverse('student_result', kwargs={
            'competition_slug': self.competition.slug,
            'student_number': self.student_number
        })
