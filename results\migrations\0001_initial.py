# Generated by Django 4.2.7 on 2025-07-11 23:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Competition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المسابقة')),
                ('year', models.IntegerField(verbose_name='السنة')),
                ('slug', models.SlugField(unique=True, verbose_name='الرابط')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'مسابقة',
                'verbose_name_plural': 'المسابقات',
                'ordering': ['-year', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_number', models.CharField(max_length=20, verbose_name='رقم الطالب')),
                ('first_name', models.CharField(max_length=100, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=100, verbose_name='اسم العائلة')),
                ('gender', models.CharField(choices=[('M', 'ذكر'), ('F', 'أنثى')], max_length=1, verbose_name='الجنس')),
            ],
            options={
                'verbose_name': 'طالب',
                'verbose_name_plural': 'الطلاب',
                'ordering': ['last_name', 'first_name'],
            },
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الشعبة')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز الشعبة')),
            ],
            options={
                'verbose_name': 'شعبة',
                'verbose_name_plural': 'الشعب',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Wilaya',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الولاية')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز الولاية')),
            ],
            options={
                'verbose_name': 'ولاية',
                'verbose_name_plural': 'الولايات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='School',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المدرسة')),
                ('wilaya', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='results.wilaya', verbose_name='الولاية')),
            ],
            options={
                'verbose_name': 'مدرسة',
                'verbose_name_plural': 'المدارس',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Result',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_number', models.CharField(max_length=20, verbose_name='رقم الطالب')),
                ('average', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='المعدل')),
                ('rank', models.IntegerField(blank=True, null=True, verbose_name='الترتيب')),
                ('rank_in_subject', models.IntegerField(blank=True, null=True, verbose_name='الترتيب في الشعبة')),
                ('status', models.CharField(choices=[('PASS', 'ناجح'), ('FAIL', 'راسب'), ('ABSENT', 'غائب')], default='PASS', max_length=10, verbose_name='الحالة')),
                ('competition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='results.competition', verbose_name='المسابقة')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='results.school', verbose_name='المدرسة')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='results.student', verbose_name='الطالب')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='results.subject', verbose_name='الشعبة')),
            ],
            options={
                'verbose_name': 'نتيجة',
                'verbose_name_plural': 'النتائج',
                'ordering': ['-average'],
                'unique_together': {('student_number', 'competition')},
            },
        ),
    ]
