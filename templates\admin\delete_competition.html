{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">الرئيسية</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module">
    <h1>{{ title }}</h1>
    
    {% if competitions %}
        <div class="alert alert-warning">
            <strong>⚠️ تحذير:</strong> حذف المسابقة سيؤدي إلى حذف جميع النتائج المرتبطة بها نهائياً. هذا الإجراء لا يمكن التراجع عنه!
        </div>
        
        <div class="competitions-list">
            {% for item in competitions %}
                <div class="competition-card">
                    <div class="competition-header">
                        <h3>🏆 {{ item.competition.name }}</h3>
                        <div class="competition-year">{{ item.competition.year }}</div>
                    </div>
                    
                    <div class="competition-stats">
                        <div class="stat-item">
                            <span class="stat-label">📊 عدد النتائج:</span>
                            <span class="stat-value">{{ item.results_count }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">📚 عدد الشعب:</span>
                            <span class="stat-value">{{ item.subjects_count }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">🔗 الرابط:</span>
                            <a href="{{ item.url }}" target="_blank" class="competition-link">{{ item.url }}</a>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">📅 تاريخ الإنشاء:</span>
                            <span class="stat-value">{{ item.competition.created_at|date:"Y-m-d H:i" }}</span>
                        </div>
                    </div>
                    
                    <div class="competition-actions">
                        <a href="{{ item.url }}" target="_blank" class="btn btn-info">
                            <i class="fas fa-eye"></i> عرض المسابقة
                        </a>
                        
                        <button type="button" 
                                class="btn btn-danger delete-btn" 
                                data-competition-id="{{ item.competition.id }}"
                                data-competition-name="{{ item.competition.name }}"
                                data-results-count="{{ item.results_count }}">
                            <i class="fas fa-trash"></i> حذف المسابقة
                        </button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info">
            <strong>ℹ️ معلومة:</strong> لا توجد مسابقات للحذف حالياً.
        </div>
    {% endif %}
</div>

<!-- نافذة التأكيد -->
<div id="deleteModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>⚠️ تأكيد الحذف</h2>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من حذف المسابقة؟</p>
            <div id="deleteDetails"></div>
            <p class="warning-text">
                <strong>تحذير:</strong> هذا الإجراء سيحذف المسابقة وجميع النتائج المرتبطة بها نهائياً ولا يمكن التراجع عنه!
            </p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDelete">إلغاء</button>
            <button type="button" class="btn btn-danger" id="confirmDelete">تأكيد الحذف</button>
        </div>
    </div>
</div>

<style>
.competitions-list {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.competition-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.competition-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.competition-header h3 {
    margin: 0;
    color: #333;
}

.competition-year {
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: bold;
}

.competition-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.stat-label {
    font-weight: bold;
    color: #666;
}

.stat-value {
    color: #333;
}

.competition-link {
    color: #007bff;
    text-decoration: none;
    font-size: 12px;
}

.competition-link:hover {
    text-decoration: underline;
}

.competition-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 0;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: #dc3545;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.warning-text {
    color: #dc3545;
    font-weight: bold;
    margin-top: 15px;
}

#deleteDetails {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin: 15px 0;
    border-left: 4px solid #dc3545;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('deleteModal');
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const closeBtn = document.querySelector('.close');
    const cancelBtn = document.getElementById('cancelDelete');
    const confirmBtn = document.getElementById('confirmDelete');
    const deleteDetails = document.getElementById('deleteDetails');
    
    let currentCompetitionId = null;
    
    // فتح نافذة التأكيد
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            currentCompetitionId = this.dataset.competitionId;
            const competitionName = this.dataset.competitionName;
            const resultsCount = this.dataset.resultsCount;
            
            deleteDetails.innerHTML = `
                <strong>المسابقة:</strong> ${competitionName}<br>
                <strong>عدد النتائج:</strong> ${resultsCount}
            `;
            
            modal.style.display = 'block';
        });
    });
    
    // إغلاق النافذة
    function closeModal() {
        modal.style.display = 'none';
        currentCompetitionId = null;
    }
    
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    
    // إغلاق عند النقر خارج النافذة
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeModal();
        }
    });
    
    // تأكيد الحذف
    confirmBtn.addEventListener('click', function() {
        if (currentCompetitionId) {
            // إرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/delete-competition/';
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            
            const competitionInput = document.createElement('input');
            competitionInput.type = 'hidden';
            competitionInput.name = 'competition_id';
            competitionInput.value = currentCompetitionId;
            
            form.appendChild(csrfInput);
            form.appendChild(competitionInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
});
</script>
{% endblock %}
