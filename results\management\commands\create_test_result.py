from django.core.management.base import BaseCommand
from results.models import Result, Student, Competition, Subject, School, Wilaya
from datetime import date

class Command(BaseCommand):
    help = 'إنشاء نتيجة تجريبية لاختبار النظام الجديد'

    def handle(self, *args, **options):
        self.stdout.write('إنشاء نتيجة تجريبية...')
        
        # إنشاء أو الحصول على البيانات الأساسية
        wilaya, _ = Wilaya.objects.get_or_create(
            code='TEST',
            defaults={'name': 'ولاية تجريبية'}
        )
        
        school, _ = School.objects.get_or_create(
            name='مدرسة تجريبية',
            defaults={'wilaya': wilaya}
        )
        
        subject, _ = Subject.objects.get_or_create(
            code='TEST',
            defaults={'name': 'شعبة تجريبية'}
        )
        
        competition, _ = Competition.objects.get_or_create(
            slug='test-competition',
            defaults={
                'name': 'مسابقة تجريبية',
                'year': 2025,
                'is_active': True
            }
        )
        
        # إنشاء طلاب بمعدلات مختلفة لاختبار الحالات الثلاث
        test_cases = [
            {'number': '99001', 'name': 'طالب راسب', 'average': 6.50},      # راسب
            {'number': '99002', 'name': 'طالب Session', 'average': 8.75},    # Session
            {'number': '99003', 'name': 'طالب ناجح', 'average': 15.25},      # ناجح
        ]
        
        for case in test_cases:
            # حذف الطالب إذا كان موجوداً
            Result.objects.filter(student_number=case['number']).delete()
            
            # إنشاء طالب جديد
            student = Student.objects.create(
                first_name=case['name'],
                last_name='تجريبي',
                birth_date=date(2005, 1, 1)
            )
            
            # إنشاء النتيجة
            result = Result.objects.create(
                competition=competition,
                student=student,
                subject=subject,
                school=school,
                student_number=case['number'],
                average=case['average'],
                rank=1
            )
            
            self.stdout.write(f'✅ تم إنشاء: {case["name"]} - رقم {case["number"]} - معدل {case["average"]} - حالة {result.status}')
        
        self.stdout.write('\n🔗 روابط الاختبار:')
        self.stdout.write('http://127.0.0.1:8000/test-competition/?search=99001  (راسب)')
        self.stdout.write('http://127.0.0.1:8000/test-competition/?search=99002  (Session)')
        self.stdout.write('http://127.0.0.1:8000/test-competition/?search=99003  (ناجح)')
        
        self.stdout.write(
            self.style.SUCCESS('\nتم إنشاء النتائج التجريبية بنجاح!')
        )
