from django.core.management.base import BaseCommand
from results.models import Result

class Command(BaseCommand):
    help = 'تحديث حالات النتائج بناءً على المعدلات الجديدة'

    def handle(self, *args, **options):
        self.stdout.write('بدء تحديث حالات النتائج...')
        
        # الحصول على جميع النتائج
        results = Result.objects.all()
        updated_count = 0
        
        for result in results:
            if result.average is not None:
                old_status = result.status
                new_status = Result.get_status_from_average(result.average)
                
                if old_status != new_status:
                    result.status = new_status
                    result.save(update_fields=['status'])
                    updated_count += 1
                    
                    self.stdout.write(
                        f'تحديث الطالب {result.student_number}: {old_status} → {new_status} (معدل: {result.average})'
                    )
        
        # إحصائيات النتائج الجديدة
        total_results = Result.objects.count()
        pass_count = Result.objects.filter(status='PASS').count()
        session_count = Result.objects.filter(status='SESSION').count()
        fail_count = Result.objects.filter(status='FAIL').count()
        absent_count = Result.objects.filter(status='ABSENT').count()
        
        self.stdout.write('\n📊 إحصائيات النتائج الجديدة:')
        self.stdout.write(f'   - إجمالي النتائج: {total_results}')
        self.stdout.write(f'   - ناجح (10+): {pass_count} ({round(pass_count/total_results*100, 1)}%)')
        self.stdout.write(f'   - Session (8-9.99): {session_count} ({round(session_count/total_results*100, 1)}%)')
        self.stdout.write(f'   - راسب (0-7.99): {fail_count} ({round(fail_count/total_results*100, 1)}%)')
        self.stdout.write(f'   - غائب: {absent_count} ({round(absent_count/total_results*100, 1)}%)')
        
        self.stdout.write(
            self.style.SUCCESS(f'\nتم تحديث {updated_count} نتيجة بنجاح!')
        )
