# 🗄️ دليل إعداد MySQL لموقع موريباك

## 🚨 المشكلة الحالية
يبدو أن MySQL غير مثبت على النظام أو غير متاح في PATH.

## 🔧 الحلول المتاحة

### الحل 1: تثبيت XAMPP (الأسهل والأسرع) ⭐

XAMPP يحتوي على MySQL + phpMyAdmin بواجهة سهلة:

#### خطوات التثبيت:
1. **تحميل XAMPP**:
   - اذهب إلى: https://www.apachefriends.org/download.html
   - حمل النسخة لـ Windows

2. **تثبيت XAMPP**:
   - شغل الملف المحمل كـ Administrator
   - اختر تثبيت Apache + MySQL + phpMyAdmin

3. **تشغيل MySQL**:
   - افتح XAMPP Control Panel
   - اضغط "Start" بجانب MySQL
   - اضغط "Start" بجانب Apache (اختياري لـ phpMyAdmin)

4. **إنشاء قاعدة البيانات**:
   - افتح المتصفح واذهب إلى: http://localhost/phpmyadmin
   - اضغط "New" في الجانب الأيسر
   - اكتب اسم قاعدة البيانات: `mauribac_results`
   - اختر Collation: `utf8mb4_unicode_ci`
   - اضغط "Create"

5. **تشغيل الموقع**:
   ```bash
   pip install mysqlclient
   python manage.py migrate
   python manage.py create_sample_data
   python manage.py runserver
   ```

### الحل 2: تثبيت MySQL Server مباشرة

#### خطوات التثبيت:
1. **تحميل MySQL**:
   - اذهب إلى: https://dev.mysql.com/downloads/mysql/
   - حمل MySQL Community Server

2. **التثبيت**:
   - شغل الملف المحمل
   - اتبع معالج التثبيت
   - احفظ كلمة مرور root

3. **إضافة MySQL للـ PATH**:
   - افتح System Properties → Environment Variables
   - أضف مجلد MySQL bin للـ PATH
   - عادة: `C:\Program Files\MySQL\MySQL Server 8.0\bin`

4. **إنشاء قاعدة البيانات**:
   ```sql
   mysql -u root -p
   CREATE DATABASE mauribac_results CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   exit
   ```

### الحل 3: استخدام SQLite (بديل سريع)

إذا كنت تريد تجربة الموقع بسرعة بدون إعداد MySQL:

```bash
python manage.py runserver --settings=mauribac_results.settings_sqlite
```

## 🎯 التوصية

**أنصح بـ XAMPP** لأنه:
- ✅ سهل التثبيت
- ✅ يحتوي على phpMyAdmin لإدارة قواعد البيانات
- ✅ لا يحتاج إعدادات معقدة
- ✅ مناسب للتطوير المحلي

## 📋 خطوات ما بعد إعداد MySQL

بعد إعداد MySQL بأي طريقة:

### 1. تحديث إعدادات Django

عدل ملف `mauribac_results/settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'mauribac_results',
        'USER': 'root',
        'PASSWORD': 'your_mysql_password',  # ضع كلمة المرور هنا
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
```

### 2. تثبيت mysqlclient

```bash
pip install mysqlclient
```

إذا فشل التثبيت، جرب:
```bash
pip install PyMySQL
```

ثم أضف هذا في أعلى ملف `mauribac_results/settings.py`:
```python
import pymysql
pymysql.install_as_MySQLdb()
```

### 3. تطبيق الهجرات

```bash
python manage.py makemigrations
python manage.py migrate
python manage.py create_sample_data
```

### 4. تشغيل الموقع

```bash
python manage.py runserver
```

## 🆘 حل المشاكل الشائعة

### مشكلة: `mysqlclient` لا يثبت
**الحل**:
```bash
pip install PyMySQL
```

### مشكلة: `Access denied for user 'root'`
**الحل**: تأكد من كلمة مرور MySQL في الإعدادات

### مشكلة: `Can't connect to MySQL server`
**الحل**: تأكد من تشغيل خدمة MySQL

### مشكلة: `Unknown database 'mauribac_results'`
**الحل**: أنشئ قاعدة البيانات يدوياً:
```sql
CREATE DATABASE mauribac_results CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 🚀 البديل السريع

إذا كنت تريد تجربة الموقع فوراً بدون إعداد MySQL:

```bash
# تشغيل بـ SQLite
python manage.py runserver --settings=mauribac_results.settings_sqlite
```

الموقع سيعمل على: http://127.0.0.1:8000/

---

## 📞 المساعدة

إذا واجهت أي مشكلة:
1. جرب الحل السريع بـ SQLite أولاً
2. ثبت XAMPP للحصول على MySQL بسهولة
3. راجع قسم حل المشاكل أعلاه

**الهدف هو تشغيل الموقع بأسرع طريقة ممكنة! 🎯**
