{% extends 'base.html' %}

{% block title %}{{ wilaya.name }} - {{ subject.name }} - {{ competition.name }} - موريباك{% endblock %}

{% block content %}
<!-- Header -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-6 mb-3">نتائج {{ wilaya.name }}</h1>
        <p class="lead">{{ subject.name }} - {{ competition.name }}</p>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                    <a href="{% url 'home' %}" class="text-white">الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'competition_detail' competition.slug %}" class="text-white">{{ competition.name }}</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'subject_results' competition.slug subject.code %}" class="text-white">{{ subject.name }}</a>
                </li>
                <li class="breadcrumb-item active text-white" aria-current="page">{{ wilaya.name }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Wilaya Info -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            معلومات الولاية
                        </h5>
                        <p class="mb-1"><strong>اسم الولاية:</strong> {{ wilaya.name }}</p>
                        <p class="mb-1"><strong>رمز الولاية:</strong> {{ wilaya.code }}</p>
                        <p class="mb-0"><strong>عدد الطلاب:</strong> {{ results.count }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-chart-bar me-2"></i>
                            إحصائيات الأداء
                        </h5>
                        {% if results %}
                        <p class="mb-1"><strong>أعلى معدل:</strong> {{ results.first.average }}</p>
                        <p class="mb-1"><strong>أقل معدل:</strong> {{ results.last.average }}</p>
                        <p class="mb-0"><strong>عدد المدارس:</strong> 
                            {{ results|regroup:"school"|length }}
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Results Table -->
<section class="py-5">
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    نتائج طلاب {{ wilaya.name }}
                </h4>
            </div>
            <div class="card-body">
                {% if results %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الترتيب</th>
                                <th>رقم الطالب</th>
                                <th>الاسم الكامل</th>
                                <th>المعدل</th>
                                <th>المدرسة</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in results %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ forloop.counter }}</span>
                                    {% if forloop.counter <= 3 %}
                                        {% if forloop.counter == 1 %}🥇
                                        {% elif forloop.counter == 2 %}🥈
                                        {% elif forloop.counter == 3 %}🥉
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>{{ result.student_number }}</td>
                                <td>
                                    <a href="{% url 'student_result' competition.slug result.student_number %}" 
                                       class="text-decoration-none fw-bold">
                                        {{ result.student.full_name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ result.average }}</span>
                                </td>
                                <td>
                                    <a href="{% url 'school_results' competition.slug subject.code result.school.name|urlencode %}" 
                                       class="text-decoration-none">
                                        {{ result.school.name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ result.status|yesno:'success,danger,secondary' }}">
                                        {{ result.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{% url 'student_result' competition.slug result.student_number %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج متاحة لهذه الولاية</h5>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="text-center mt-4">
            <a href="{% url 'subject_results' competition.slug subject.code %}" class="btn btn-primary me-2">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى الشعبة
            </a>
            <a href="{% url 'competition_detail' competition.slug %}" class="btn btn-outline-primary">
                <i class="fas fa-home me-2"></i>
                العودة إلى المسابقة
            </a>
        </div>
    </div>
</section>
{% endblock %}
