from django.core.management.base import BaseCommand
from results.models import Competition, Subject, Wilaya, School, Student, Result
import random
from datetime import date, timedelta

class Command(BaseCommand):
    help = 'إعداد سريع للبيانات التجريبية'

    def handle(self, *args, **options):
        self.stdout.write('بدء الإعداد السريع...')
        
        # 1. إنشاء الولايات الأساسية
        self.create_basic_wilayas()
        
        # 2. إنشاء الشعب
        self.create_subjects()
        
        # 3. إنشاء المدارس
        self.create_schools()
        
        # 4. إنشاء المسابقات
        self.create_competitions()
        
        # 5. إنشاء بيانات تجريبية
        self.create_sample_data()
        
        self.stdout.write(
            self.style.SUCCESS('تم الإعداد السريع بنجاح!')
        )
    
    def create_basic_wilayas(self):
        """إنشاء الولايات الأساسية"""
        wilayas_data = [
            ('انواكشوط الشمالية', 'NKC'),
            ('انواكشوط الجنوبية', 'NKS'),
            ('انواكشوط الغربية', 'NKW'),
            ('داخلت انواذيبو', 'DN'),
            ('إينشيري', 'IC'),
            ('آدرار', 'AD'),
            ('البراكنة', 'BR'),
            ('ترارزة', 'TR')
        ]
        
        for name, code in wilayas_data:
            Wilaya.objects.get_or_create(
                code=code,
                defaults={'name': name}
            )
        
        self.stdout.write('✅ تم إنشاء الولايات')
    
    def create_subjects(self):
        """إنشاء الشعب"""
        subjects_data = [
            ('العلوم الطبيعية', 'SN'),
            ('الرياضيات', 'MT'),
            ('الآداب الأصلية', 'LO'),
            ('الآداب العصرية', 'LM'),
            ('العلوم الإنسانية', 'SH')
        ]
        
        for name, code in subjects_data:
            Subject.objects.get_or_create(
                code=code,
                defaults={'name': name}
            )
        
        self.stdout.write('✅ تم إنشاء الشعب')
    
    def create_schools(self):
        """إنشاء المدارس"""
        wilayas = Wilaya.objects.all()[:4]  # أول 4 ولايات
        school_names = ['ثانوية النجاح', 'ثانوية التفوق', 'ثانوية الأمل']
        
        for wilaya in wilayas:
            for school_name in school_names:
                School.objects.get_or_create(
                    name=f"{school_name} - {wilaya.name}",
                    wilaya=wilaya
                )
        
        self.stdout.write('✅ تم إنشاء المدارس')
    
    def create_competitions(self):
        """إنشاء المسابقات"""
        competitions_data = [
            ('باكالوريا 2025', 2025, 'bac-2025-newResults'),
            ('الباكلوريا 2024', 2024, 'bac-2024-uKolupoGL'),
            ('مسابقة ختم الدروس الإعدادية 2024', 2024, 'bepc-2024-eeIOq3sks'),
            ('مسابقة الإمتياز - الثانوية 2024', 2024, 'e5c-2024-1OijYskae')
        ]
        
        for name, year, slug in competitions_data:
            Competition.objects.get_or_create(
                slug=slug,
                defaults={
                    'name': name,
                    'year': year,
                    'is_active': True
                }
            )
        
        self.stdout.write('✅ تم إنشاء المسابقات')
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية"""
        competitions = Competition.objects.all()
        subjects = Subject.objects.all()
        schools = School.objects.all()
        
        first_names = ['محمد', 'أحمد', 'علي', 'فاطمة', 'عائشة', 'زينب', 'حسن', 'عمر', 'سارة', 'مريم']
        last_names = ['أحمد', 'محمد', 'علي', 'حسن', 'إبراهيم', 'عبد الله', 'يوسف', 'خالد', 'سعد', 'عمر']
        
        start_numbers = {
            'bac-2025-newResults': 40000,
            'bac-2024-uKolupoGL': 30000,
            'bepc-2024-eeIOq3sks': 20000,
            'e5c-2024-1OijYskae': 10000
        }
        
        total_created = 0
        
        for competition in competitions:
            start_num = start_numbers.get(competition.slug, 50000)
            
            # إنشاء 20 طالب لكل مسابقة
            for i in range(1, 21):
                # تاريخ ميلاد عشوائي
                today = date.today()
                birth_date = today - timedelta(days=random.randint(16*365, 22*365))
                
                # إنشاء الطالب
                student = Student.objects.create(
                    first_name=random.choice(first_names),
                    last_name=random.choice(last_names),
                    birth_date=birth_date
                )
                
                # إنشاء النتيجة مع توزيع متنوع للمعدلات
                # 60% ناجح (10-19), 25% Session (8-9.99), 15% راسب (0-7.99)
                rand = random.random()
                if rand < 0.15:  # 15% راسب
                    average = round(random.uniform(0.0, 7.99), 2)
                elif rand < 0.40:  # 25% Session
                    average = round(random.uniform(8.0, 9.99), 2)
                else:  # 60% ناجح
                    average = round(random.uniform(10.0, 19.0), 2)

                Result.objects.create(
                    competition=competition,
                    student=student,
                    subject=random.choice(subjects),
                    school=random.choice(schools),
                    student_number=f"{start_num + i}",
                    average=average,
                    rank=i
                    # الحالة ستحدد تلقائياً في save()
                )
                
                total_created += 1
            
            # إعادة حساب الترتيب
            results = Result.objects.filter(
                competition=competition
            ).order_by('-average')
            
            for rank, result in enumerate(results, 1):
                result.rank = rank
                result.save(update_fields=['rank'])
            
            self.stdout.write(f'  ✅ {competition.name}: 20 طالب')
        
        self.stdout.write(f'✅ تم إنشاء {total_created} طالب ونتيجة')
