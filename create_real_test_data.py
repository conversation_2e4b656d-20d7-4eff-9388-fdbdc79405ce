#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mauribac_results.settings')
django.setup()

from results.models import Competition, Subject, Wilaya, School, Student, Result

def create_real_test_data():
    print("إنشاء بيانات تجريبية حقيقية...")
    
    try:
        # حذف البيانات القديمة
        Result.objects.all().delete()
        Student.objects.all().delete()
        School.objects.all().delete()
        Wilaya.objects.all().delete()
        Subject.objects.all().delete()
        Competition.objects.all().delete()
        
        # إنشاء مسابقة
        competition = Competition.objects.create(
            name='باكالوريا 2025',
            slug='bac-2025-newResults',
            year=2025,
            is_active=True
        )
        print(f"✅ تم إنشاء المسابقة: {competition.name}")
        
        # إنشاء شعب
        subjects = [
            {'name': 'العلوم الطبيعية', 'code': 'SN'},
            {'name': 'الرياضيات', 'code': 'SM'},
            {'name': 'الآداب الأصلية', 'code': 'LO'},
            {'name': 'الآداب العصرية', 'code': 'LM'},
        ]
        
        subject_objects = {}
        for subj_data in subjects:
            subject = Subject.objects.create(**subj_data)
            subject_objects[subj_data['code']] = subject
            print(f"✅ تم إنشاء الشعبة: {subject.name}")
        
        # إنشاء ولايات
        wilayas = [
            {'name': 'انواكشوط الشمالية', 'code': 'NKC'},
            {'name': 'انواكشوط الغربية', 'code': 'NKT'},
            {'name': 'انواكشوط الجنوبية', 'code': 'NKS'},
            {'name': 'آدرار', 'code': 'ADR'},
        ]
        
        wilaya_objects = {}
        for wil_data in wilayas:
            wilaya = Wilaya.objects.create(**wil_data)
            wilaya_objects[wil_data['code']] = wilaya
            print(f"✅ تم إنشاء الولاية: {wilaya.name}")
        
        # إنشاء مدارس
        schools_data = [
            {'name': 'ثانوية النجاح', 'wilaya': 'NKC'},
            {'name': 'ثانوية الأرقام', 'wilaya': 'NKC'},
            {'name': 'ثانوية التفوق', 'wilaya': 'NKT'},
            {'name': 'معهد الهندسة', 'wilaya': 'ADR'},
        ]
        
        school_objects = {}
        for school_data in schools_data:
            school = School.objects.create(
                name=school_data['name'],
                wilaya=wilaya_objects[school_data['wilaya']]
            )
            school_objects[school_data['name']] = school
            print(f"✅ تم إنشاء المدرسة: {school.name}")
        
        # إنشاء طلاب ونتائج
        students_data = [
            {
                'number': '40001',
                'first_name': 'محمد',
                'last_name': 'عبد الله',
                'gender': 'M',
                'subject': 'SN',
                'school': 'ثانوية النجاح',
                'average': 15.50,
                'rank': 1
            },
            {
                'number': '40002',
                'first_name': 'فاطمة',
                'last_name': 'أحمد',
                'gender': 'F',
                'subject': 'SN',
                'school': 'ثانوية النجاح',
                'average': 16.25,
                'rank': 2
            },
            {
                'number': '40003',
                'first_name': 'عبد الله',
                'last_name': 'محمد',
                'gender': 'M',
                'subject': 'SM',
                'school': 'ثانوية الأرقام',
                'average': 17.00,
                'rank': 1
            },
            {
                'number': '40004',
                'first_name': 'مريم',
                'last_name': 'علي',
                'gender': 'F',
                'subject': 'LO',
                'school': 'ثانوية التفوق',
                'average': 18.75,
                'rank': 1
            },
            {
                'number': '40005',
                'first_name': 'أحمد',
                'last_name': 'إبراهيم',
                'gender': 'M',
                'subject': 'LM',
                'school': 'معهد الهندسة',
                'average': 14.25,
                'rank': 3
            },
            {
                'number': '40006',
                'first_name': 'عائشة',
                'last_name': 'يوسف',
                'gender': 'F',
                'subject': 'SN',
                'school': 'ثانوية النجاح',
                'average': 13.75,
                'rank': 5
            },
        ]
        
        for student_data in students_data:
            # إنشاء الطالب
            student = Student.objects.create(
                first_name=student_data['first_name'],
                last_name=student_data['last_name'],
                gender=student_data['gender']
            )
            
            # إنشاء النتيجة
            result = Result.objects.create(
                competition=competition,
                student=student,
                subject=subject_objects[student_data['subject']],
                school=school_objects[student_data['school']],
                student_number=student_data['number'],
                average=student_data['average'],
                rank=student_data['rank'],
                status='PASS' if student_data['average'] >= 10 else 'FAIL'
            )
            
            print(f"✅ تم إنشاء الطالب: {student.full_name} - رقم {student_data['number']} - معدل {student_data['average']}")
        
        print(f"\n🎉 تم إنشاء البيانات بنجاح!")
        print(f"📊 إجمالي النتائج: {Result.objects.count()}")
        print(f"👥 إجمالي الطلاب: {Student.objects.count()}")
        print(f"🏫 إجمالي المدارس: {School.objects.count()}")
        print(f"🗺️ إجمالي الولايات: {Wilaya.objects.count()}")
        print(f"📚 إجمالي الشعب: {Subject.objects.count()}")
        
        print("\n🔍 أرقام للاختبار:")
        for student_data in students_data:
            print(f"  - {student_data['number']}: {student_data['first_name']} {student_data['last_name']}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_real_test_data()
