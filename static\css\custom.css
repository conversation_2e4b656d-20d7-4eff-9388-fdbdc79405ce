/* ملف CSS مخصص لموقع موريباك */

/* تحسينات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* تحسينات للنصوص العربية */
.arabic-text {
    font-family: '<PERSON><PERSON>', 'Times New Roman', serif;
    font-size: 1.1em;
}

/* تحسينات للبطاقات */
.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-radius: 10px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

/* تحسينات للأزرار */
.btn {
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* تحسينات للجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* تحسينات للشارات */
.badge {
    border-radius: 15px;
    padding: 8px 12px;
    font-size: 0.9em;
}

/* تحسينات للبحث */
.search-box {
    max-width: 600px;
    margin: 0 auto;
}

.search-box .form-control {
    border-radius: 25px;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    font-size: 1.1em;
}

.search-box .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تحسينات للقسم البطولي */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,100 1000,0"/></svg>');
    background-size: cover;
}

/* تحسينات للطلاب الأوائل */
.top-student {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border: 2px solid #ffd700;
}

.top-student .card-body {
    position: relative;
}

.top-student::before {
    content: '👑';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 2em;
    z-index: 10;
}

/* تحسينات للإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.stats-card h3 {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 0;
}

/* تحسينات للتنقل */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: rgba(255, 255, 255, 0.7);
}

/* تحسينات للفوتر */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.social-links a {
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1.5rem;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #667eea;
    transform: translateY(-3px);
    color: white;
}

/* تحسينات للرسائل */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .search-box .form-control {
        font-size: 1rem;
        padding: 12px 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar,
    .footer,
    .btn,
    .breadcrumb {
        display: none !important;
    }
    
    .hero-section {
        background: white !important;
        color: black !important;
        padding: 1rem 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
