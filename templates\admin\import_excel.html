{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">الرئيسية</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module">
    <h1>{{ title }}</h1>
    
    <div class="form-row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>📊 استيراد نتائج من ملف Excel</h3>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" class="form">
                        {% csrf_token %}
                        
                        <div class="form-group">
                            <label for="{{ form.excel_file.id_for_label }}">{{ form.excel_file.label }}</label>
                            {{ form.excel_file }}
                            <small class="form-text text-muted">{{ form.excel_file.help_text }}</small>
                            {% if form.excel_file.errors %}
                                <div class="text-danger">{{ form.excel_file.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.competition.id_for_label }}">{{ form.competition.label }}</label>
                            {{ form.competition }}
                            <small class="form-text text-muted">{{ form.competition.help_text }}</small>
                            {% if form.competition.errors %}
                                <div class="text-danger">{{ form.competition.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                {{ form.create_new_competition }}
                                <label class="form-check-label" for="{{ form.create_new_competition.id_for_label }}">
                                    {{ form.create_new_competition.label }}
                                </label>
                            </div>
                            <small class="form-text text-muted">{{ form.create_new_competition.help_text }}</small>
                        </div>

                        <div class="form-group" id="new-competition-fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="{{ form.new_competition_name.id_for_label }}">{{ form.new_competition_name.label }}</label>
                                    {{ form.new_competition_name }}
                                    <small class="form-text text-muted">{{ form.new_competition_name.help_text }}</small>
                                    {% if form.new_competition_name.errors %}
                                        <div class="text-danger">{{ form.new_competition_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="{{ form.new_competition_year.id_for_label }}">{{ form.new_competition_year.label }}</label>
                                    {{ form.new_competition_year }}
                                    <small class="form-text text-muted">{{ form.new_competition_year.help_text }}</small>
                                    {% if form.new_competition_year.errors %}
                                        <div class="text-danger">{{ form.new_competition_year.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                {{ form.overwrite_existing }}
                                <label class="form-check-label" for="{{ form.overwrite_existing.id_for_label }}">
                                    {{ form.overwrite_existing.label }}
                                </label>
                            </div>
                            <small class="form-text text-muted">{{ form.overwrite_existing.help_text }}</small>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> استيراد النتائج
                            </button>
                            <a href="/admin/download-template/" class="btn btn-secondary">
                                <i class="fas fa-download"></i> تحميل قالب Excel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4>📋 تعليمات الاستيراد</h4>
                </div>
                <div class="card-body">
                    <h5>📁 تنسيق الملف:</h5>
                    <ul>
                        <li>ملف Excel (.xlsx أو .xls)</li>
                        <li>حجم أقل من 10 ميجابايت</li>
                        <li>الصف الأول يحتوي على رؤوس الأعمدة</li>
                    </ul>
                    
                    <h5>📊 الأعمدة المطلوبة:</h5>
                    <ul>
                        <li><strong>رقم_الطالب</strong> - رقم الطالب الفريد</li>
                        <li><strong>الاسم_الأول</strong> - الاسم الأول للطالب</li>
                        <li><strong>اسم_العائلة</strong> - اسم العائلة</li>
                        <li><strong>الشعبة</strong> - الشعبة الدراسية</li>
                        <li><strong>المدرسة</strong> - اسم المدرسة</li>
                        <li><strong>الولاية</strong> - اسم الولاية</li>
                        <li><strong>المعدل</strong> - المعدل النهائي (رقم)</li>
                    </ul>
                    
                    <h5>⚠️ ملاحظات مهمة:</h5>
                    <ul>
                        <li>سيتم إنشاء الشعب والمدارس والولايات تلقائياً إذا لم تكن موجودة</li>
                        <li>المعدل أقل من 10 = راسب، 10 فأكثر = ناجح</li>
                        <li>سيتم تحديث الترتيب تلقائياً بعد الاستيراد</li>
                        <li><strong>المسابقة الجديدة ستظهر في الصفحة الرئيسية فوراً</strong></li>
                    </ul>

                    <h5>🆕 إنشاء مسابقة جديدة:</h5>
                    <ul>
                        <li>حدد "إنشاء مسابقة جديدة" لإنشاء مسابقة جديدة</li>
                        <li>اتركه فارغاً لاستخدام اسم الملف كاسم للمسابقة</li>
                        <li>ستظهر المسابقة الجديدة في الصفحة الرئيسية تلقائياً</li>
                    </ul>
                    
                    <div class="alert alert-info">
                        <strong>💡 نصيحة:</strong> حمل قالب Excel أولاً لمعرفة التنسيق الصحيح
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 15px;
    font-weight: bold;
}

.card-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.btn {
    padding: 10px 20px;
    margin-right: 10px;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.alert {
    padding: 15px;
    border-radius: 5px;
    margin-top: 15px;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.text-danger {
    color: #dc3545;
}

.form-text {
    font-size: 0.875em;
    color: #6c757d;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding-right: 15px;
    padding-left: 15px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const createNewCheckbox = document.getElementById('{{ form.create_new_competition.id_for_label }}');
    const newCompetitionFields = document.getElementById('new-competition-fields');
    const competitionSelect = document.getElementById('{{ form.competition.id_for_label }}');

    function toggleFields() {
        if (createNewCheckbox.checked) {
            newCompetitionFields.style.display = 'block';
            competitionSelect.disabled = true;
            competitionSelect.required = false;
        } else {
            newCompetitionFields.style.display = 'none';
            competitionSelect.disabled = false;
            competitionSelect.required = true;
        }
    }

    createNewCheckbox.addEventListener('change', toggleFields);
    toggleFields(); // تطبيق الحالة الأولية
});
</script>
{% endblock %}
