@echo off
chcp 65001 >nul
echo 🚀 بدء تشغيل موقع موريباك...

echo 📦 تثبيت Django...
pip install django

echo 🔄 إنشاء الهجرات...
python manage.py makemigrations results --settings=mauribac_results.settings_sqlite

echo 🔄 تطبيق الهجرات...
python manage.py migrate --settings=mauribac_results.settings_sqlite

echo 📊 إنشاء البيانات التجريبية...
python manage.py create_sample_data --settings=mauribac_results.settings_sqlite

echo.
echo 🌐 تشغيل خادم Django...
echo 📍 الموقع متاح على: http://127.0.0.1:8000/
echo 🔧 لوحة الإدارة: http://127.0.0.1:8000/admin/
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo.

python manage.py runserver --settings=mauribac_results.settings_sqlite

pause
