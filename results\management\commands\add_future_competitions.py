from django.core.management.base import BaseCommand
from results.models import Competition, Subject, Wilaya, School, Student, Result
from django.utils.text import slugify
import random
from faker import Faker

fake = Faker('ar_SA')  # استخدام اللغة العربية

class Command(BaseCommand):
    help = 'إضافة مسابقات مستقبلية وماضية تلقائياً'

    def add_arguments(self, parser):
        parser.add_argument(
            '--year',
            type=int,
            help='السنة المراد إضافة مسابقات لها',
            default=2026
        )
        parser.add_argument(
            '--count',
            type=int,
            help='عدد المسابقات المراد إضافتها',
            default=3
        )

    def handle(self, *args, **options):
        year = options['year']
        count = options['count']
        
        self.stdout.write(f'إضافة {count} مسابقات للسنة {year}...')
        
        # قوائم أسماء المسابقات المحتملة
        competition_types = [
            'الباكلوريا',
            'مسابقة ختم الدروس الإعدادية',
            'مسابقة الإمتياز - الثانوية',
            'مسابقة التعليم الأساسي',
            'مسابقة الدخول للجامعة',
            'مسابقة المعاهد العليا',
            'مسابقة التكوين المهني'
        ]
        
        # إنشاء المسابقات
        for i in range(count):
            competition_name = f"{random.choice(competition_types)} {year}"
            
            # التأكد من عدم تكرار الاسم
            counter = 1
            original_name = competition_name
            while Competition.objects.filter(name=competition_name).exists():
                competition_name = f"{original_name} - {counter}"
                counter += 1
            
            # إنشاء slug فريد
            base_slug = slugify(competition_name, allow_unicode=True)
            slug = f"{base_slug}-{random.randint(1000, 9999)}"
            
            # التأكد من عدم تكرار الـ slug
            while Competition.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{random.randint(1000, 9999)}"
            
            # إنشاء المسابقة
            competition = Competition.objects.create(
                name=competition_name,
                year=year,
                slug=slug,
                is_active=True
            )
            
            self.stdout.write(f'✅ تم إنشاء المسابقة: {competition_name}')
            
            # إضافة بيانات تجريبية للمسابقة الجديدة
            self.add_sample_results(competition, year)
    
    def add_sample_results(self, competition, year):
        """إضافة نتائج تجريبية للمسابقة الجديدة"""
        
        # الحصول على الشعب والمدارس الموجودة
        subjects = list(Subject.objects.all())
        schools = list(School.objects.all())
        
        if not subjects or not schools:
            self.stdout.write('⚠️ لا توجد شعب أو مدارس، يرجى إنشاؤها أولاً')
            return
        
        # تحديد نطاق أرقام الطلاب حسب السنة
        if year >= 2026:
            start_num = 50000
        elif year >= 2025:
            start_num = 40000
        elif year >= 2024:
            start_num = 30000
        elif year >= 2023:
            start_num = 20000
        else:
            start_num = 10000
        
        # إنشاء 20 طالب لكل مسابقة جديدة
        for i in range(1, 21):
            # إنشاء الطالب
            student = Student.objects.create(
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                birth_date=fake.date_of_birth(minimum_age=16, maximum_age=22)
            )
            
            # إنشاء النتيجة
            average = round(random.uniform(8.0, 20.0), 2)
            
            Result.objects.create(
                competition=competition,
                student=student,
                subject=random.choice(subjects),
                school=random.choice(schools),
                student_number=f"{start_num + i}",
                average=average,
                rank=i,  # ترتيب مؤقت
                status='PASS' if average >= 10 else 'FAIL'
            )
        
        self.stdout.write(f'✅ تم إضافة 20 نتيجة تجريبية للمسابقة {competition.name}')
        
        # إعادة حساب الترتيب الصحيح
        self.recalculate_ranks(competition)
    
    def recalculate_ranks(self, competition):
        """إعادة حساب الترتيب حسب المعدل"""
        results = Result.objects.filter(competition=competition).order_by('-average')
        
        for rank, result in enumerate(results, 1):
            result.rank = rank
            result.save()
        
        self.stdout.write(f'✅ تم إعادة حساب الترتيب للمسابقة {competition.name}')
