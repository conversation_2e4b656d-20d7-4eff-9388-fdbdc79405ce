# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        default-libmysqlclient-dev \
        build-essential \
        pkg-config \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات
COPY requirements.txt /app/

# تثبيت متطلبات Python
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات المشروع
COPY . /app/

# إنشاء مجلدات الملفات الثابتة والسجلات
RUN mkdir -p /app/staticfiles /app/media /app/logs

# جمع الملفات الثابتة
RUN python manage.py collectstatic --noinput --settings=mauribac_results.settings_production

# تعيين الصلاحيات
RUN chmod +x /app/run_server.py

# فتح المنفذ
EXPOSE 8000

# تشغيل التطبيق
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000", "--settings=mauribac_results.settings_production"]
