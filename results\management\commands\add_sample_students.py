from django.core.management.base import BaseCommand
from results.models import Competition, Subject, Wilaya, School, Student, Result
import random
from datetime import date, timedelta

class Command(BaseCommand):
    help = 'إضافة طلاب تجريبيين إضافيين'

    def add_arguments(self, parser):
        parser.add_argument(
            '--competition',
            type=str,
            help='slug المسابقة المراد إضافة طلاب لها',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='عدد الطلاب المراد إضافتهم',
        )

    def handle(self, *args, **options):
        competition_slug = options.get('competition')
        count = options['count']
        
        if competition_slug:
            try:
                competition = Competition.objects.get(slug=competition_slug)
                self.add_students_to_competition(competition, count)
            except Competition.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'المسابقة غير موجودة: {competition_slug}')
                )
                return
        else:
            # إضافة طلاب لجميع المسابقات
            competitions = Competition.objects.filter(is_active=True)
            for competition in competitions:
                self.add_students_to_competition(competition, count)
        
        self.stdout.write(
            self.style.SUCCESS('تم إضافة الطلاب بنجاح!')
        )
    
    def add_students_to_competition(self, competition, count):
        """إضافة طلاب لمسابقة محددة"""
        subjects = Subject.objects.all()
        schools = School.objects.all()
        
        if not subjects.exists() or not schools.exists():
            self.stdout.write(
                self.style.WARNING('لا توجد شعب أو مدارس، يرجى تشغيل quick_setup أولاً')
            )
            return
        
        # أسماء عربية بسيطة
        first_names = [
            'محمد', 'أحمد', 'علي', 'حسن', 'حسين', 'عبد الله', 'عمر', 'يوسف', 'إبراهيم', 'خالد',
            'فاطمة', 'عائشة', 'خديجة', 'زينب', 'مريم', 'سارة', 'نور', 'هدى', 'أمل', 'رقية'
        ]
        
        last_names = [
            'أحمد', 'محمد', 'علي', 'حسن', 'إبراهيم', 'عبد الله', 'يوسف', 'عمر', 'خالد', 'سعد',
            'الأحمد', 'المحمد', 'العلي', 'الحسن', 'الإبراهيم', 'العبد الله', 'اليوسف', 'العمر'
        ]
        
        # تحديد رقم البداية حسب المسابقة
        start_numbers = {
            'bac-2025-newResults': 40000,
            'bac-2024-uKolupoGL': 30000,
            'bepc-2024-eeIOq3sks': 20000,
            'e5c-2024-1OijYskae': 10000
        }
        
        base_num = start_numbers.get(competition.slug, 50000)
        
        # العثور على آخر رقم مستخدم
        last_result = Result.objects.filter(
            competition=competition
        ).order_by('-student_number').first()
        
        if last_result:
            try:
                last_num = int(last_result.student_number)
                start_num = last_num + 1
            except ValueError:
                start_num = base_num + 100
        else:
            start_num = base_num + 1
        
        created_count = 0
        
        for i in range(count):
            # تاريخ ميلاد عشوائي
            today = date.today()
            birth_date = today - timedelta(days=random.randint(16*365, 22*365))
            
            # إنشاء الطالب
            student = Student.objects.create(
                first_name=random.choice(first_names),
                last_name=random.choice(last_names),
                birth_date=birth_date
            )
            
            # إنشاء النتيجة مع توزيع متنوع
            rand = random.random()
            if rand < 0.15:  # 15% راسب
                average = round(random.uniform(0.0, 7.99), 2)
            elif rand < 0.40:  # 25% Session
                average = round(random.uniform(8.0, 9.99), 2)
            else:  # 60% ناجح
                average = round(random.uniform(10.0, 19.5), 2)

            Result.objects.create(
                competition=competition,
                student=student,
                subject=random.choice(subjects),
                school=random.choice(schools),
                student_number=str(start_num + i),
                average=average,
                rank=1  # سيتم إعادة حسابه
                # الحالة ستحدد تلقائياً
            )
            
            created_count += 1
        
        # إعادة حساب الترتيب
        self.recalculate_ranks(competition)
        
        self.stdout.write(f'✅ تم إضافة {created_count} طالب للمسابقة: {competition.name}')
    
    def recalculate_ranks(self, competition):
        """إعادة حساب الترتيب"""
        results = Result.objects.filter(
            competition=competition
        ).order_by('-average', 'student__first_name')
        
        for rank, result in enumerate(results, 1):
            result.rank = rank
            result.save(update_fields=['rank'])
