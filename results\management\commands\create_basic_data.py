from django.core.management.base import BaseCommand
from results.models import Wilaya, School, Subject, Competition, Student, Result
import random

class Command(BaseCommand):
    help = 'إنشاء بيانات أساسية للاختبار'

    def handle(self, *args, **options):
        self.stdout.write('🚀 إنشاء البيانات الأساسية...')
        
        # إنشاء الولايات
        wilayas_data = [
            {'code': '01', 'name': 'أدرار'},
            {'code': '02', 'name': 'الشلف'},
            {'code': '03', 'name': 'الأغواط'},
            {'code': '16', 'name': 'الجزائر'},
            {'code': '31', 'name': 'وهران'},
        ]
        
        wilayas = []
        for wilaya_data in wilayas_data:
            wilaya, created = Wilaya.objects.get_or_create(
                code=wilaya_data['code'],
                defaults={'name': wilaya_data['name']}
            )
            wilayas.append(wilaya)
            if created:
                self.stdout.write(f'✅ تم إنشاء الولاية: {wilaya.name}')
        
        # إنشاء المدارس
        schools = []
        for i, wilaya in enumerate(wilayas):
            for j in range(3):  # 3 مدارس لكل ولاية
                school, created = School.objects.get_or_create(
                    name=f'ثانوية {wilaya.name} {j+1}',
                    defaults={'wilaya': wilaya}
                )
                schools.append(school)
                if created:
                    self.stdout.write(f'✅ تم إنشاء المدرسة: {school.name}')
        
        # إنشاء الشعب
        subjects_data = [
            {'code': 'SM', 'name': 'علوم تجريبية'},
            {'code': 'MT', 'name': 'رياضيات'},
            {'code': 'GE', 'name': 'تسيير واقتصاد'},
            {'code': 'LF', 'name': 'آداب وفلسفة'},
            {'code': 'LE', 'name': 'لغات أجنبية'},
        ]
        
        subjects = []
        for subject_data in subjects_data:
            subject, created = Subject.objects.get_or_create(
                code=subject_data['code'],
                defaults={'name': subject_data['name']}
            )
            subjects.append(subject)
            if created:
                self.stdout.write(f'✅ تم إنشاء الشعبة: {subject.name}')
        
        # إنشاء المسابقة
        competition, created = Competition.objects.get_or_create(
            slug='bac-2024-test',
            defaults={
                'name': 'بكالوريا 2024',
                'year': 2024,
                'is_active': True
            }
        )
        if created:
            self.stdout.write(f'✅ تم إنشاء المسابقة: {competition.name}')
        
        # إنشاء الطلاب والنتائج
        self.stdout.write('📝 إنشاء الطلاب والنتائج...')
        
        for i in range(100):  # 100 طالب
            # إنشاء طالب
            student, created = Student.objects.get_or_create(
                student_number=f'2024{i+1:04d}',
                defaults={
                    'first_name': f'طالب{i+1}',
                    'last_name': f'اختبار',
                    'gender': random.choice(['M', 'F'])
                }
            )
            
            if created:
                # إنشاء نتيجة للطالب
                # توزيع النتائج: 60% ناجح، 25% Session، 15% راسب
                rand = random.random()
                if rand < 0.15:  # 15% راسب
                    average = round(random.uniform(0.0, 7.99), 2)
                elif rand < 0.40:  # 25% Session
                    average = round(random.uniform(8.0, 9.99), 2)
                else:  # 60% ناجح
                    average = round(random.uniform(10.0, 19.0), 2)
                
                Result.objects.create(
                    competition=competition,
                    student=student,
                    subject=random.choice(subjects),
                    school=random.choice(schools),
                    student_number=student.student_number,
                    average=average,
                    rank=i+1
                )
        
        # عرض الإحصائيات النهائية
        self.stdout.write('\n📊 الإحصائيات النهائية:')
        self.stdout.write(f'🌍 الولايات: {Wilaya.objects.count()}')
        self.stdout.write(f'🏫 المدارس: {School.objects.count()}')
        self.stdout.write(f'📚 الشعب: {Subject.objects.count()}')
        self.stdout.write(f'🏆 المسابقات: {Competition.objects.count()}')
        self.stdout.write(f'👥 الطلاب: {Student.objects.count()}')
        self.stdout.write(f'📋 النتائج: {Result.objects.count()}')
        
        # إحصائيات النتائج
        results_count = Result.objects.count()
        if results_count > 0:
            passed = Result.objects.filter(status='PASS').count()
            session = Result.objects.filter(status='SESSION').count()
            failed = Result.objects.filter(status='FAIL').count()
            
            self.stdout.write(f'\n📈 توزيع النتائج:')
            self.stdout.write(f'✅ ناجح: {passed} ({round(passed/results_count*100, 1)}%)')
            self.stdout.write(f'⚠️ Session: {session} ({round(session/results_count*100, 1)}%)')
            self.stdout.write(f'❌ راسب: {failed} ({round(failed/results_count*100, 1)}%)')
        
        self.stdout.write(
            self.style.SUCCESS('\n🎉 تم إنشاء البيانات الأساسية بنجاح!')
        )
