{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">الرئيسية</a>
    &rsaquo; <a href="/admin/import-excel/">استيراد Excel</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module">
    <h1>{{ title }}</h1>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3>📥 تحميل قالب Excel</h3>
                </div>
                <div class="card-body">
                    <form method="post" class="form">
                        {% csrf_token %}
                        
                        <div class="form-group">
                            <label for="{{ form.competition.id_for_label }}">{{ form.competition.label }}</label>
                            {{ form.competition }}
                            <small class="form-text text-muted">{{ form.competition.help_text }}</small>
                            {% if form.competition.errors %}
                                <div class="text-danger">{{ form.competition.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-download"></i> تحميل القالب
                            </button>
                            <a href="/admin/import-excel/" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للاستيراد
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>📋 معلومات القالب</h4>
                </div>
                <div class="card-body">
                    <h5>📊 محتويات القالب:</h5>
                    <ul>
                        <li>رؤوس الأعمدة بالتنسيق الصحيح</li>
                        <li>3 صفوف من البيانات التجريبية</li>
                        <li>تنسيق مناسب للطباعة والعرض</li>
                        <li>ألوان مميزة للرؤوس</li>
                    </ul>
                    
                    <h5>🔧 كيفية الاستخدام:</h5>
                    <ol>
                        <li>حمل القالب</li>
                        <li>احذف البيانات التجريبية</li>
                        <li>أضف بياناتك الحقيقية</li>
                        <li>احفظ الملف</li>
                        <li>ارفعه في صفحة الاستيراد</li>
                    </ol>
                    
                    <div class="alert alert-success">
                        <strong>✅ مميزات القالب:</strong><br>
                        - تنسيق تلقائي للأعمدة<br>
                        - رؤوس ملونة وواضحة<br>
                        - بيانات تجريبية للمساعدة<br>
                        - متوافق مع جميع إصدارات Excel
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 15px;
    font-weight: bold;
}

.card-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.btn {
    padding: 10px 20px;
    margin-right: 10px;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.alert {
    padding: 15px;
    border-radius: 5px;
    margin-top: 15px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.text-danger {
    color: #dc3545;
}

.form-text {
    font-size: 0.875em;
    color: #6c757d;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}
</style>
{% endblock %}
