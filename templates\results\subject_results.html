{% extends 'base.html' %}

{% block title %}{{ subject.name }} - {{ competition.name }} - موريباك{% endblock %}

{% block content %}
<!-- Header -->
<section class="hero-section">
    <div class="container text-center">
        <h1 class="display-6 mb-3">نتائج {{ subject.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                    <a href="{% url 'home' %}" class="text-white">الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'competition_detail' competition.slug %}" class="text-white">{{ competition.name }}</a>
                </li>
                <li class="breadcrumb-item active text-white" aria-current="page">{{ subject.name }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Statistics -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h3 class="text-primary">{{ total_students }}</h3>
                        <p class="mb-0">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h3 class="text-success">{{ passed_students }}</h3>
                        <p class="mb-0">الناجحون</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h3 class="text-info">{{ passed_students|floatformat:0 }}%</h3>
                        <p class="mb-0">نسبة النجاح</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Results Table -->
<section class="py-5">
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة النتائج - {{ subject.name }}
                </h4>
            </div>
            <div class="card-body">
                {% if results %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الترتيب</th>
                                <th>رقم الطالب</th>
                                <th>الاسم الكامل</th>
                                <th>المعدل</th>
                                <th>المدرسة</th>
                                <th>الولاية</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in results %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ forloop.counter }}</span>
                                    {% if forloop.counter <= 3 %}
                                        {% if forloop.counter == 1 %}🥇
                                        {% elif forloop.counter == 2 %}🥈
                                        {% elif forloop.counter == 3 %}🥉
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>{{ result.student_number }}</td>
                                <td>
                                    <a href="{% url 'student_result' competition.slug result.student_number %}" 
                                       class="text-decoration-none fw-bold">
                                        {{ result.student.full_name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ result.average }}</span>
                                </td>
                                <td>
                                    <a href="{% url 'school_results' competition.slug subject.code result.school.name|urlencode %}" 
                                       class="text-decoration-none">
                                        {{ result.school.name }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{% url 'wilaya_results' competition.slug subject.code result.school.wilaya.name|urlencode %}" 
                                       class="text-decoration-none">
                                        {{ result.school.wilaya.name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ result.status|yesno:'success,danger,secondary' }}">
                                        {{ result.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{% url 'student_result' competition.slug result.student_number %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج متاحة</h5>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Back Button -->
        <div class="text-center mt-4">
            <a href="{% url 'competition_detail' competition.slug %}" class="btn btn-primary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى المسابقة
            </a>
        </div>
    </div>
</section>
{% endblock %}
